#!/usr/bin/env node

/**
 * Automated Plugin Migration Script
 * Migrates plugins from createPlugin() wrapper pattern to direct BasePlugin usage
 * Following the established 4-step migration pattern
 */

import fs from 'fs';
import path from 'path';

function migratePlugin(filePath) {
  console.log(`Migrating: ${filePath}`);

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Step 1: Remove plugin-factory import
  const oldImportPattern = /import { createPlugin } from ['"]\.\.\/plugin-factory['"];\s*\n/g;
  if (oldImportPattern.test(content)) {
    content = content.replace(oldImportPattern, '');
    modified = true;
    console.log(`  ✓ Removed plugin-factory import`);
  }

  // Also remove Plugin type import if it exists
  const pluginTypePattern = /import type { Editor, Plugin } from ['"]\.\.\/\.\.\/types['"];/g;
  if (pluginTypePattern.test(content)) {
    content = content.replace(pluginTypePattern, "import type { Editor } from '../../types';");
    modified = true;
    console.log(`  ✓ Updated type imports`);
  }

  // Step 2: Change handleCommand visibility from protected to public
  const handleCommandPattern = /(\s+)protected(\s+(?:override\s+)?handleCommand\s*\([^)]*\)\s*:\s*void\s*{)/g;
  if (handleCommandPattern.test(content)) {
    content = content.replace(handleCommandPattern, '$1public$2');
    modified = true;
    console.log(`  ✓ Changed handleCommand to public`);
  }

  // Step 3: Fix toolbarButtons references (replace with DOM query)
  const toolbarButtonsPattern = /(\s+)const button = this\.toolbarButtons\[0\];/g;
  if (toolbarButtonsPattern.test(content)) {
    // Extract command from the plugin config to create proper selector
    const commandMatch = content.match(/command:\s*['"]([^'"]+)['"]/);
    if (commandMatch) {
      const command = commandMatch[1];
      content = content.replace(
        toolbarButtonsPattern,
        `$1// Find the toolbar button by command\n$1const button = document.querySelector('[data-command="${command}"]') as HTMLButtonElement;`
      );
      modified = true;
      console.log(`  ✓ Fixed toolbarButtons reference for command: ${command}`);
    }
  }

  // Step 4: Replace createPlugin wrapper with direct export
  const createPluginPattern = /\/\/ Create a plugin that conforms to the Plugin interface\s*\nconst plugin: Plugin = createPlugin<[^>]+>\(\s*[^,]+,\s*\([^)]*\)\s*=>\s*{\s*const instance = new ([^(]+)\(\);\s*instance\.init\(editor\);\s*return instance;\s*},\s*\([^)]*\)\s*=>\s*{\s*[^}]*\.destroy\(\);\s*}\s*\);/gs;

  const createPluginMatch = content.match(createPluginPattern);
  if (createPluginMatch) {
    // Extract the class name from the createPlugin call - improved pattern
    const classNameMatch = createPluginMatch[0].match(/new\s+(\w+Plugin)\(\)/);

    if (classNameMatch && classNameMatch[1]) {
      const className = classNameMatch[1];

      // Validate that the extracted class name ends with "Plugin"
      if (className.endsWith('Plugin')) {
        content = content.replace(
          createPluginPattern,
          `// Create and export the plugin instance directly\nconst plugin = new ${className}();`
        );
        modified = true;
        console.log(`  ✓ Replaced createPlugin wrapper with direct export (${className})`);
      } else {
        console.log(`  ⚠️  Warning: Extracted class name "${className}" doesn't end with "Plugin", skipping replacement`);
      }
    } else {
      // Fallback: try to extract class name from the file content
      const classDefinitionMatch = content.match(/class\s+(\w+Plugin)\s+extends\s+BasePlugin/);
      if (classDefinitionMatch && classDefinitionMatch[1]) {
        const className = classDefinitionMatch[1];
        content = content.replace(
          createPluginPattern,
          `// Create and export the plugin instance directly\nconst plugin = new ${className}();`
        );
        modified = true;
        console.log(`  ✓ Replaced createPlugin wrapper with direct export (${className} - fallback)`);
      } else {
        console.log(`  ❌ Error: Could not extract class name for createPlugin replacement`);
      }
    }
  }

  // Remove deprecated registerToolbarItems calls
  const registerToolbarPattern = /(\s+)this\.registerToolbarItems\(\);/g;
  if (registerToolbarPattern.test(content)) {
    content = content.replace(registerToolbarPattern, '$1// Note: Toolbar items are now handled by ToolbarLayoutManager');
    modified = true;
    console.log(`  ✓ Removed deprecated registerToolbarItems call`);
  }

  // Remove unused Editor import if no longer needed
  if (!content.includes('(editor: Editor)') && content.includes("import type { Editor } from '../../types';")) {
    content = content.replace(/import type { Editor } from ['"]\.\.\/\.\.\/types['"];\s*\n/, '');
    modified = true;
    console.log(`  ✓ Removed unused Editor import`);
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Migration complete for ${path.basename(filePath)}`);
    return true;
  } else {
    console.log(`  ⏭️  No changes needed for ${path.basename(filePath)}`);
    return false;
  }
}

function migrateIndexFile(filePath) {
  console.log(`Migrating index file: ${filePath}`);

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Remove plugin-factory import
  const importPattern = /import { createPlugin } from ['"]\.\.\/plugin-factory['"];\s*\n/g;
  if (importPattern.test(content)) {
    content = content.replace(importPattern, '');
    modified = true;
  }

  // Replace createPlugin group wrapper with direct array export
  const groupPluginPattern = /\/\/ Create a group plugin that registers all [^]*?plugins\.forEach\([^]*?\}\);[^]*?\);[^]*?export default groupPlugin;/gs;
  if (groupPluginPattern.test(content)) {
    // Extract the plugins array name from the content
    const arrayMatch = content.match(/export const (\w+Plugins): Plugin\[\] = \[/);
    const arrayName = arrayMatch ? arrayMatch[1] : 'Plugins';

    content = content.replace(
      groupPluginPattern,
      `// Export the plugins array directly\n// PluginManager will handle individual plugin registration\nexport default ${arrayName};`
    );
    modified = true;
    console.log(`  ✓ Replaced group plugin wrapper with direct array export (${arrayName})`);
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ Index file migration complete`);
    return true;
  } else {
    console.log(`  ⏭️  No changes needed for index file`);
    return false;
  }
}

// Main execution
const targetDir = process.argv[2] || 'src/plugins/media';
const files = fs.readdirSync(targetDir);

console.log(`🚀 Starting automated plugin migration for ${targetDir} directory...\n`);

let totalMigrated = 0;

files.forEach(file => {
  const filePath = path.join(targetDir, file);

  if (file.endsWith('.ts') && file !== 'index.ts') {
    if (migratePlugin(filePath)) {
      totalMigrated++;
    }
    console.log('');
  }
});

// Migrate index file last
const indexPath = path.join(targetDir, 'index.ts');
if (fs.existsSync(indexPath)) {
  if (migrateIndexFile(indexPath)) {
    totalMigrated++;
  }
}

console.log(`\n✅ Migration complete! ${totalMigrated} files modified.`);
console.log('🔍 Running TypeScript check...\n');
