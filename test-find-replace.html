<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Find-Replace Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-button {
            background: #28a745;
        }
        .test-button:hover {
            background: #1e7e34;
        }
        #toolbar {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        #toolbar button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }
        #toolbar button:hover {
            background: #5a6268;
        }
        #editor {
            border: 1px solid #ccc;
            padding: 15px;
            min-height: 150px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <h1>Find-Replace Plugin Debug Test</h1>

    <div class="debug-panel">
        <h3>Test Actions</h3>
        <button onclick="testDirectCommand()" class="test-button">Test Direct Command</button>
        <button onclick="testToolbarButton()" class="test-button">Test Toolbar Button Click</button>
        <button onclick="testKeyboardShortcut()" class="test-button">Test Keyboard Shortcut (Ctrl+F)</button>
        <button onclick="testDirectPluginCall()" class="test-button">Test Direct Plugin Call</button>
        <button onclick="testSimpleDialog()" class="test-button">Test Simple Dialog</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>

    <div class="debug-panel">
        <h3>Debug Output</h3>
        <div id="debug-output" class="debug-output"></div>
    </div>

    <div class="debug-panel">
        <h3>Toolbar</h3>
        <div id="toolbar">
            <!-- Toolbar will be populated by the application -->
        </div>
    </div>

    <div class="debug-panel">
        <h3>Editor</h3>
        <div id="editor" contenteditable="true">
            This is some sample text for testing the find and replace functionality.
            You can search for words like "sample", "text", "functionality", or "testing".
            Try searching for repeated words like "the" or "and" to see multiple matches.
        </div>
    </div>

    <script type="module">
        import './src/main.ts';

        let debugOutput = document.getElementById('debug-output');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.textContent += `[${timestamp}] ${message}\n`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }

        function clearOutput() {
            debugOutput.textContent = '';
        }

        // Make functions global for onclick handlers
        window.log = log;
        window.clearOutput = clearOutput;

        // Override console methods to capture debug messages
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;
        const originalConsoleError = console.error;

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            log('LOG: ' + args.join(' '));
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            log('WARN: ' + args.join(' '));
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            log('ERROR: ' + args.join(' '));
        };

        window.testDirectCommand = function() {
            log('Testing direct command dispatch...');
            const event = new CustomEvent('feather:command', {
                detail: {
                    command: 'find-replace',
                    pluginId: 'find-replace',
                    source: 'debug-test'
                }
            });
            document.dispatchEvent(event);
        };

        window.testToolbarButton = function() {
            log('Testing toolbar button click simulation...');
            const button = document.querySelector('[data-command="find-replace"]');
            if (button) {
                log('Found find-replace button, clicking...');
                button.click();
            } else {
                log('ERROR: Find-replace button not found in toolbar!');
                // List all toolbar buttons
                const allButtons = document.querySelectorAll('#toolbar button[data-command]');
                log(`Found ${allButtons.length} toolbar buttons:`);
                allButtons.forEach(btn => {
                    log(`  - ${btn.getAttribute('data-command')} (${btn.textContent || btn.innerHTML})`);
                });
            }
        };

        window.testKeyboardShortcut = function() {
            log('Testing keyboard shortcut (Ctrl+F)...');
            const event = new KeyboardEvent('keydown', {
                key: 'f',
                ctrlKey: true,
                bubbles: true
            });
            document.dispatchEvent(event);
        };

        window.testDirectPluginCall = function() {
            log('Testing direct plugin method call...');

            // Try method 1: Direct plugin access
            if (window.findReplacePlugin) {
                log('Found findReplacePlugin globally, calling handleCommand directly...');
                window.findReplacePlugin.handleCommand('find-replace');
                return;
            }

            // Try method 2: Plugin manager access
            if (window.featherPluginManager) {
                const plugin = window.featherPluginManager.get('find-replace');
                if (plugin) {
                    log('Found find-replace plugin via plugin manager, calling handleCommand directly...');
                    plugin.handleCommand('find-replace');
                } else {
                    log('ERROR: find-replace plugin not found in plugin manager');
                }
            } else {
                log('ERROR: Neither findReplacePlugin nor featherPluginManager available globally');
            }
        };

        window.testSimpleDialog = function() {
            log('Testing simple dialog creation...');
            if (window.findReplacePlugin) {
                log('Calling testDialog method...');
                window.findReplacePlugin.testDialog();
            } else {
                log('ERROR: findReplacePlugin not available globally');
            }
        };

        // Listen for feather:command events
        document.addEventListener('feather:command', (event) => {
            log(`Received feather:command event: ${JSON.stringify(event.detail)}`);
        });

        // Wait for page to load and check plugin status
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, checking plugin status...');

                // Check if editor is ready
                const editor = document.getElementById('editor');
                log(`Editor element found: ${!!editor}`);

                // Check if toolbar exists
                const toolbar = document.getElementById('toolbar');
                log(`Toolbar element found: ${!!toolbar}`);

                if (toolbar) {
                    const buttons = toolbar.querySelectorAll('button[data-command]');
                    log(`Toolbar buttons found: ${buttons.length}`);
                    buttons.forEach(btn => {
                        const command = btn.getAttribute('data-command');
                        log(`  - Button: ${command} (${btn.textContent || btn.innerHTML})`);
                    });
                }

                // Check for find-replace specific elements
                const findReplaceButton = document.querySelector('[data-command="find-replace"]');
                log(`Find-replace button found: ${!!findReplaceButton}`);

                // Check if any dialogs are already open
                const dialogs = document.querySelectorAll('[role="dialog"]');
                log(`Open dialogs: ${dialogs.length}`);

                // Check global theme system
                log(`Global theme manager: ${!!(window as any).featherThemeManager}`);
                log(`Global element factory: ${!!(window as any).featherElementFactory}`);

            }, 2000);
        });
    </script>
</body>
</html>
