/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    tailwindcss(),
  ],
  server: {
    open: true
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/main.ts'),
      name: 'RichTextEditor',
      fileName: (format) => `editor.${format === 'es' ? 'mjs' : 'umd.js'}`
    },
    rollupOptions: {
      output: {
        globals: {}
      }
    },
    sourcemap: true,
    minify: 'terser',
    cssCodeSplit: false
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/setupTests.ts'],
  },
});