import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

/**
 * Import/Export plugin configuration
 */
const config: PluginConfig = {
  id: 'import-export',
  name: 'Import/Export',
  description: 'Import and export content in different formats: HTML, Markdown, DOCX, JSON',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'import-export',
      command: 'import-export',
      icon: '⇆',
      label: 'Import/Export',
      tooltip: 'Import or export content',
      group: 'utilities',
      ariaLabel: 'Import or export content',
    }
  ],
  shortcuts: [] // No shortcuts for import/export
};

/**
 * Import/Export plugin implementation
 * Allows importing and exporting content in different formats
 */
export class ImportExportPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;

  constructor() {
    super(config);
  }

  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }

  /**
   * Handle the import/export command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    if (_command === 'import-export') {
      if (this.isDialogOpen) {
        this.closeDialog();
      } else {
        this.openDialog();
      }
    }
  }

  /**
   * Open the import/export dialog
   */
  private openDialog(): void {
    if (this.isDialogOpen) return;

    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/30 z-[9998]';
    document.body.appendChild(backdrop);

    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[480px] bg-white dark:bg-slate-800 rounded-lg shadow-xl p-5 z-[9999] font-sans text-gray-900 dark:text-slate-200 flex flex-col';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'import-export-dialog-title');

    // Header
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-4 pb-3 border-b border-gray-200 dark:border-slate-700';
    const title = document.createElement('h3');
    title.id = 'import-export-dialog-title';
    title.className = 'font-semibold text-base m-0';
    title.textContent = 'Import/Export Content';
    const closeButton = document.createElement('button');
    closeButton.type = 'button';
    closeButton.className = 'bg-transparent border-none text-lg cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 leading-none';
    closeButton.setAttribute('aria-label', 'Close');
    closeButton.textContent = '×';
    header.appendChild(title);
    header.appendChild(closeButton);
    this.dialog.appendChild(header);

    // Tabs
    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'flex border-b border-gray-200 dark:border-slate-700 mb-5';
    const exportTab = document.createElement('div');
    exportTab.className = 'px-4 py-2.5 cursor-pointer text-sm border-b-2 font-medium transition-all duration-200 border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400 active'; // Active by default
    exportTab.dataset.tab = 'export';
    exportTab.textContent = 'Export';
    const importTab = document.createElement('div');
    importTab.className = 'px-4 py-2.5 cursor-pointer text-sm border-b-2 border-transparent transition-all duration-200 text-gray-600 dark:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700 hover:border-gray-300 dark:hover:border-slate-600';
    importTab.dataset.tab = 'import';
    importTab.textContent = 'Import';
    tabsContainer.appendChild(exportTab);
    tabsContainer.appendChild(importTab);
    this.dialog.appendChild(tabsContainer);

    // Content Area
    const contentArea = document.createElement('div');
    contentArea.className = 'min-h-[200px] flex-grow';

    // Export Panel
    const exportPanel = document.createElement('div');
    exportPanel.className = 'block'; // Active by default
    exportPanel.dataset.panel = 'export';

    let label = document.createElement('label') as HTMLLabelElement;
    label.htmlFor = 'export-format';
    label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    label.textContent = 'Choose format';
    exportPanel.appendChild(label);

    const exportFormatSelect = document.createElement('select');
    exportFormatSelect.id = 'export-format';
    exportFormatSelect.className = 'w-full p-2.5 rounded border border-gray-300 dark:border-slate-600 mb-4 text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    ['html', 'markdown', 'text', 'json'].forEach(formatValue => { // Renamed 'format' to 'formatValue' to avoid conflict
      const option = document.createElement('option');
      option.value = formatValue;
      option.textContent = formatValue.charAt(0).toUpperCase() + formatValue.slice(1);
      exportFormatSelect.appendChild(option);
    });
    exportPanel.appendChild(exportFormatSelect);

    const exportTextarea = document.createElement('textarea');
    exportTextarea.id = 'export-content';
    exportTextarea.className = 'w-full h-[200px] p-3 rounded border border-gray-300 dark:border-slate-600 text-[13px] leading-normal resize-y mb-4 bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    exportTextarea.readOnly = true;
    exportPanel.appendChild(exportTextarea);

    const exportFooter = document.createElement('div');
    exportFooter.className = 'flex justify-end mt-2';
    const copyButtonElem = document.createElement('button');
    copyButtonElem.type = 'button';
    copyButtonElem.id = 'copy-button';
    copyButtonElem.className = 'py-2.5 px-4 border-none rounded bg-blue-600 hover:bg-blue-700 text-white text-sm cursor-pointer transition-colors duration-200 disabled:bg-gray-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed';
    copyButtonElem.textContent = 'Copy to Clipboard';
    exportFooter.appendChild(copyButtonElem);
    exportPanel.appendChild(exportFooter);
    contentArea.appendChild(exportPanel);

    // Import Panel
    const importPanel = document.createElement('div');
    importPanel.className = 'hidden';
    importPanel.dataset.panel = 'import';

    label = document.createElement('label') as HTMLLabelElement; // Re-assign label
    label.htmlFor = 'import-format';
    label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    label.textContent = 'Choose format';
    importPanel.appendChild(label);

    const importFormatSelect = document.createElement('select');
    importFormatSelect.id = 'import-format';
    importFormatSelect.className = 'w-full p-2.5 rounded border border-gray-300 dark:border-slate-600 mb-4 text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    ['html', 'markdown', 'text', 'json'].forEach(formatValue => { // Renamed 'format' to 'formatValue'
      const option = document.createElement('option');
      option.value = formatValue;
      option.textContent = formatValue.charAt(0).toUpperCase() + formatValue.slice(1);
      importFormatSelect.appendChild(option);
    });
    importPanel.appendChild(importFormatSelect);

    const importDropzoneLabel = document.createElement('label');
    importDropzoneLabel.id = 'import-dropzone';
    importDropzoneLabel.className = 'block py-3 px-5 border-2 border-dashed border-gray-400 dark:border-slate-500 rounded-md cursor-pointer text-center text-sm text-gray-600 dark:text-slate-300 transition-all duration-200 mb-4 hover:border-blue-500 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-slate-700';
    const dropzoneText = document.createElement('div');
    dropzoneText.className = 'feather-import-export-dropzone-text';
    dropzoneText.textContent = 'Drop file here or paste content below';
    importDropzoneLabel.appendChild(dropzoneText);
    const importFileInput = document.createElement('input');
    importFileInput.type = 'file';
    importFileInput.id = 'import-file-input';
    importFileInput.className = 'hidden';
    importDropzoneLabel.appendChild(importFileInput);
    importPanel.appendChild(importDropzoneLabel);

    const importFilenameDisplay = document.createElement('p');
    importFilenameDisplay.id = 'import-filename-display';
    importFilenameDisplay.className = 'text-[13px] text-gray-500 dark:text-slate-400 mt-2 mb-2 hidden';
    importPanel.appendChild(importFilenameDisplay);

    const importTextarea = document.createElement('textarea');
    importTextarea.id = 'import-content';
    importTextarea.className = 'w-full h-[150px] p-3 rounded border border-gray-300 dark:border-slate-600 text-[13px] leading-normal resize-y mb-4 bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    importTextarea.placeholder = 'Paste content here...';
    importPanel.appendChild(importTextarea);

    const importFooter = document.createElement('div');
    importFooter.className = 'flex justify-end mt-2';
    const importButtonElem = document.createElement('button');
    importButtonElem.type = 'button';
    importButtonElem.id = 'import-button';
    importButtonElem.className = 'py-2.5 px-4 border-none rounded bg-blue-600 hover:bg-blue-700 text-white text-sm cursor-pointer transition-colors duration-200 disabled:bg-gray-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed';
    importButtonElem.textContent = 'Import to Editor';
    importFooter.appendChild(importButtonElem);
    importPanel.appendChild(importFooter);
    contentArea.appendChild(importPanel);

    this.dialog.appendChild(contentArea);

    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;

    this.setupDialogEventListeners();
    this.updateExportContent('html'); // Default to HTML export view
    document.addEventListener('keydown', this.handleDialogKeydown);
  }

  /**
   * Setup all event listeners for the dialog
   */
  private setupDialogEventListeners(): void {
    if (!this.dialog) return;

    const tabs = this.dialog.querySelectorAll('[data-tab]');
    const panels = this.dialog.querySelectorAll('[data-panel]');

    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const tabId = (tab as HTMLElement).dataset.tab;

        tabs.forEach(t => {
          const htmlTab = t as HTMLElement;
          // Reset all tabs to inactive style
          htmlTab.className = 'px-4 py-2.5 cursor-pointer text-sm border-b-2 border-transparent transition-all duration-200 text-gray-600 dark:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700 hover:border-gray-300 dark:hover:border-slate-600';
          htmlTab.classList.remove('active'); // Remove any 'active' marker class
        });
        // Set active tab style
        tab.className = 'px-4 py-2.5 cursor-pointer text-sm border-b-2 font-medium transition-all duration-200 border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400 active';

        panels.forEach(panel => {
          if ((panel as HTMLElement).dataset.panel === tabId) {
            panel.classList.remove('hidden');
            panel.classList.add('block');
          } else {
            panel.classList.remove('block');
            panel.classList.add('hidden');
          }
        });
        // Update export content if export tab is selected
        if (tabId === 'export') {
            const exportFormat = this.dialog?.querySelector('#export-format') as HTMLSelectElement;
            if(exportFormat) this.updateExportContent(exportFormat.value);
        }
      });
    });

    const closeButton = this.dialog.querySelector('.bg-transparent.border-none.text-lg');
    closeButton?.addEventListener('click', () => this.closeDialog());

    const backdrop = document.querySelector('.fixed.inset-0.bg-black\\/30');
    backdrop?.addEventListener('click', () => this.closeDialog());

    const exportFormat = this.dialog.querySelector('#export-format') as HTMLSelectElement;
    exportFormat?.addEventListener('change', () => this.updateExportContent(exportFormat.value));

    const copyButton = this.dialog.querySelector('#copy-button');
    copyButton?.addEventListener('click', () => {
      const exportContent = this.dialog?.querySelector('#export-content') as HTMLTextAreaElement;
      if (exportContent) {
        exportContent.select();
        navigator.clipboard.writeText(exportContent.value).then(() => {
          if (copyButton instanceof HTMLElement) {
            const originalText = copyButton.textContent;
            copyButton.textContent = 'Copied!';
            setTimeout(() => { if (copyButton) copyButton.textContent = originalText; }, 2000);
          }
        }).catch(err => console.error('Failed to copy: ', err));
      }
    });

    const dropzone = this.dialog.querySelector('#import-dropzone') as HTMLLabelElement;
    const fileInput = this.dialog.querySelector('#import-file-input') as HTMLInputElement;
    const filenameDisplay = this.dialog.querySelector('#import-filename-display') as HTMLParagraphElement;

    dropzone?.addEventListener('click', () => {
        fileInput?.click();
    });

    dropzone?.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropzone.classList.add('border-blue-500', 'dark:border-blue-400', 'bg-blue-50', 'dark:bg-slate-700');
    });

    dropzone?.addEventListener('dragleave', () => {
      dropzone.classList.remove('border-blue-500', 'dark:border-blue-400', 'bg-blue-50', 'dark:bg-slate-700');
    });

    dropzone?.addEventListener('drop', (e) => {
      e.preventDefault();
      dropzone.classList.remove('border-blue-500', 'dark:border-blue-400', 'bg-blue-50', 'dark:bg-slate-700');
      if (e instanceof DragEvent && e.dataTransfer?.files.length) {
        this.handleFileInput(e.dataTransfer.files[0]);
        if (filenameDisplay) {
            filenameDisplay.textContent = `File: ${e.dataTransfer.files[0].name}`;
            filenameDisplay.classList.remove('hidden');
        }
      }
    });

    fileInput?.addEventListener('change', () => {
      if (fileInput.files?.length) {
        this.handleFileInput(fileInput.files[0]);
         if (filenameDisplay) {
            filenameDisplay.textContent = `File: ${fileInput.files[0].name}`;
            filenameDisplay.classList.remove('hidden');
        }
      } else {
        if(filenameDisplay) filenameDisplay.classList.add('hidden');
      }
    });

    const importButton = this.dialog.querySelector('#import-button');
    importButton?.addEventListener('click', () => {
      const importContent = this.dialog?.querySelector('#import-content') as HTMLTextAreaElement;
      const importFormat = this.dialog?.querySelector('#import-format') as HTMLSelectElement;
      if (importContent && importFormat) {
        this.importContent(importContent.value, importFormat.value);
      }
    });
  }

  /**
   * Handle file input for import
   * @param file The file to import
   */
  private handleFileInput(file: File): void {
    if (!this.dialog) return;

    const reader = new FileReader();
    const importContent = this.dialog.querySelector('#import-content') as HTMLTextAreaElement;
    const importFormat = this.dialog.querySelector('#import-format') as HTMLSelectElement;
    const filenameDisplay = this.dialog.querySelector('#import-filename-display') as HTMLParagraphElement;

    const extension = file.name.split('.').pop()?.toLowerCase();
    if (extension) {
      switch (extension) {
        case 'html': case 'htm': importFormat.value = 'html'; break;
        case 'md': case 'markdown': importFormat.value = 'markdown'; break;
        case 'txt': importFormat.value = 'text'; break;
        case 'json': importFormat.value = 'json'; break;
      }
    }

    reader.onload = (e) => {
      if (e.target?.result) {
        importContent.value = e.target.result as string;
        if (filenameDisplay) {
            filenameDisplay.textContent = `File: ${file.name}`;
            filenameDisplay.classList.remove('hidden');
        }
      }
    };
    reader.readAsText(file);
  }

  /**
   * Update the export content based on the selected format
   * @param format The format to export as
   */
  private updateExportContent(format: string): void {
    if (!this.editor || !this.dialog) return;

    const exportContent = this.dialog.querySelector('#export-content') as HTMLTextAreaElement;
    if (!exportContent) return;

    switch (format) {
      case 'html':
        exportContent.value = this.editor.getElement().innerHTML;
        break;
      case 'text':
        exportContent.value = this.editor.getElement().textContent || '';
        break;
      case 'markdown':
        exportContent.value = this.convertHtmlToMarkdown(this.editor.getElement().innerHTML);
        break;
      case 'json': {
        const jsonData = {
          type: 'doc',
          content: this.editor.getElement().innerHTML,
          metadata: { timestamp: new Date().toISOString() }
        };
        exportContent.value = JSON.stringify(jsonData, null, 2);
      }
        break;
    }
  }

  /**
   * Simple HTML to Markdown converter
   * Note: For a production plugin, a more robust library would be recommended
   * @param html HTML content to convert
   * @returns Markdown content
   */
  private convertHtmlToMarkdown(html: string): string {
    const temp = document.createElement('div');
    temp.innerHTML = html;
    const processNode = (node: Node): string => {
      if (node.nodeType === Node.TEXT_NODE) return node.textContent || '';
      if (node.nodeType === Node.ELEMENT_NODE && node instanceof HTMLElement) {
        const tag = node.tagName.toLowerCase();
        const childContent = Array.from(node.childNodes).map(child => processNode(child)).join('');
        switch (tag) {
          case 'h1': return `# ${childContent}\n\n`;
          case 'h2': return `## ${childContent}\n\n`;
          case 'h3': return `### ${childContent}\n\n`;
          case 'h4': return `#### ${childContent}\n\n`;
          case 'h5': return `##### ${childContent}\n\n`;
          case 'h6': return `###### ${childContent}\n\n`;
          case 'p': return `${childContent}\n\n`;
          case 'strong': case 'b': return `**${childContent}**`;
          case 'em': case 'i': return `*${childContent}*`;
          case 'code': {
            // If it's an inline code, keep it as is. If it's part of a <pre>, the <pre> handles ```
            return node.parentElement?.tagName === 'PRE' ? childContent : "`" + childContent + "`";
          }
          case 'pre': {
            const codeContent = node.querySelector('code')?.textContent?.trim() || childContent.trim();
            return "```\n" + codeContent + "\n```\n\n";
          }
          case 'a': return `[${childContent}](${node.getAttribute('href') || ''})`;
          case 'img': return `![${node.getAttribute('alt') || ''}](${node.getAttribute('src') || ''})`;
          case 'blockquote': return `> ${childContent.replace(/\n+/g, '\n> ')}\n\n`; // Ensure multiple lines in bq are prefixed
          case 'ul': return Array.from(node.children).map(li => `- ${processNode(li).trim()}`).join('\n') + '\n\n';
          case 'ol': return Array.from(node.children).map((li, index) => `${index + 1}. ${processNode(li).trim()}`).join('\n') + '\n\n';
          case 'hr': return '---\n\n';
          case 'br': return '\n';
          default: return childContent;
        }
      }
      return '';
    };
    return processNode(temp).trim().replace(/\n{3,}/g, '\n\n'); // Normalize multiple blank lines
  }

  /**
   * Convert Markdown to HTML
   * Note: For a production plugin, a more robust library would be recommended
   * @param markdown Markdown content to convert
   * @returns HTML content
   */
  private convertMarkdownToHtml(markdown: string): string {
    const lines = markdown.split('\n');
    let html = '';
    let inList = '';
    let inCodeBlock = false;
    let inBlockquote = false;

    const escapeHtml = (text: string): string => {
        return text.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
    };

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];

      if (line.trim().startsWith('```')) {
        if (inCodeBlock) {
          html += '</code></pre>\n'; inCodeBlock = false;
        } else {
          const lang = line.trim().substring(3).trim(); // Lexical declaration
          html += lang ? `<pre><code class="language-${lang}">` : '<pre><code>';
          inCodeBlock = true;
        }
        continue;
      }
      if (inCodeBlock) { html += escapeHtml(line) + '\n'; continue; }

      const closeListIfNeeded = (newLineType?: string) => {
        if (inList && inList !== newLineType) {
          html += (inList === 'ul' ? '</ul>\n' : '</ol>\n');
          inList = '';
        }
      };

      if (line.trim().startsWith('>')) {
        if (!inBlockquote) { html += '<blockquote>\n'; inBlockquote = true; }
        line = line.replace(/^>\s?/, '');
        html += `<p>${this.processInlineMarkdown(line)}</p>\n`;
      } else {
        if (inBlockquote) { html += '</blockquote>\n'; inBlockquote = false; }

        if (line.startsWith('# ')) html += `<h1>${this.processInlineMarkdown(line.substring(2))}</h1>\n`;
        else if (line.startsWith('## ')) html += `<h2>${this.processInlineMarkdown(line.substring(3))}</h2>\n`;
        else if (line.startsWith('### ')) html += `<h3>${this.processInlineMarkdown(line.substring(4))}</h3>\n`;
        else if (line.startsWith('#### ')) html += `<h4>${this.processInlineMarkdown(line.substring(5))}</h4>\n`;
        else if (line.startsWith('##### ')) html += `<h5>${this.processInlineMarkdown(line.substring(6))}</h5>\n`;
        else if (line.startsWith('###### ')) html += `<h6>${this.processInlineMarkdown(line.substring(7))}</h6>\n`;
        else if (line.match(/^\s*-\s+/)) {
          closeListIfNeeded('ul');
          if (!inList) { html += '<ul>\n'; inList = 'ul'; }
          html += `<li>${this.processInlineMarkdown(line.replace(/^\s*-\s+/, ''))}</li>\n`;
        } else if (line.match(/^\s*\d+\.\s+/)) {
          closeListIfNeeded('ol');
          if (!inList) { html += '<ol>\n'; inList = 'ol'; }
          html += `<li>${this.processInlineMarkdown(line.replace(/^\s*\d+\.\s+/, ''))}</li>\n`;
        } else if (line.trim() === '---') {
          closeListIfNeeded(); html += '<hr>\n';
        } else if (line.trim().length > 0) {
          closeListIfNeeded(); html += `<p>${this.processInlineMarkdown(line)}</p>\n`;
        } else {
          closeListIfNeeded();
        }
      }
    }
    if (inList) html += (inList === 'ul' ? '</ul>\n' : '</ol>\n');
    if (inBlockquote) html += '</blockquote>\n';
    if (inCodeBlock) html += '</code></pre>\n';
    return html;
  }

  /**
   * Process inline markdown formatting
   * @param text Text with markdown
   * @returns HTML formatted text
   */
  private processInlineMarkdown(text: string): string {
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/__(.*?)__/g, '<strong>$1</strong>');
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>').replace(/_(.*?)_/g, '<em>$1</em>');
    text = text.replace(/`(.*?)`/g, '<code>$1</code>');
    text = text.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1">'); // Image before link
    text = text.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
    return text;
  }

  /**
   * Import content into the editor
   * @param content The content to import
   * @param format The format of the content
   */
  private importContent(content: string, format: string): void {
    if (!this.editor) return;
    let htmlContent = '';
    switch (format) {
      case 'html': htmlContent = this.sanitizeHtml(content); break;
      case 'text': htmlContent = content.replace(/\n/g, '<br>'); break;
      case 'markdown': htmlContent = this.convertMarkdownToHtml(content); break;
      case 'json':
        try {
          const jsonContent = JSON.parse(content);
          if (jsonContent.content && typeof jsonContent.content === 'string') {
            htmlContent = this.sanitizeHtml(jsonContent.content);
          } else { console.error('Invalid JSON structure for import.'); return; }
        } catch (e) { console.error('Invalid JSON:', e); return; }
        break;
    }
    const editorElement = this.editor.getElement();
    editorElement.innerHTML = htmlContent;
    editorElement.dispatchEvent(new InputEvent('input', { bubbles: true, cancelable: true }));
    this.closeDialog();
  }

  /**
   * Simple HTML sanitizer
   * @param html HTML to sanitize
   * @returns Sanitized HTML
   */
  private sanitizeHtml(html: string): string {
    const temp = document.createElement('div');
    temp.innerHTML = html;
    const scripts = temp.querySelectorAll('script');
    scripts.forEach(script => script.remove());
    const styles = temp.querySelectorAll('style');
    styles.forEach(style => style.remove());
    const allElements = temp.querySelectorAll('*');
    allElements.forEach(el => {
      Array.from(el.attributes).forEach(attr => {
        if (attr.name.startsWith('on')) el.removeAttribute(attr.name);
      });
      if (el instanceof HTMLAnchorElement && el.href.toLowerCase().startsWith('javascript:')) el.href = '#';
    });
    return temp.innerHTML;
  }

  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeDialog();
    }
  };

  /**
   * Close the dialog
   */
  private closeDialog(): void {
    if (!this.isDialogOpen) return;
    const backdrop = document.querySelector('.fixed.inset-0.bg-black\\/30');
    if (backdrop?.parentNode) backdrop.parentNode.removeChild(backdrop);
    if (this.dialog?.parentNode) this.dialog.parentNode.removeChild(this.dialog);
    this.dialog = null;
    this.isDialogOpen = false;
    document.removeEventListener('keydown', this.handleDialogKeydown);
    if (this.editor) this.editor.focus();
    const styleTag = document.getElementById('feather-import-export-styles');
    styleTag?.remove();
  }

  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    if (this.isDialogOpen) {
      this.closeDialog();
    }
  }
}

// Create and export the plugin instance directly
const plugin = new ImportExportPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
