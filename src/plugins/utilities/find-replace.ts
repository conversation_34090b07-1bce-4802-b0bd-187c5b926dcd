import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

/**
 * Find and Replace plugin configuration
 */
const config: PluginConfig = {
  id: 'find-replace',
  name: 'Find and Replace',
  description: 'Search and replace text in the editor content',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'find-replace',
      command: 'find-replace',
      icon: '🔍',
      label: 'Find & Replace',
      tooltip: 'Find and replace text (Ctrl/⌘+F)',
      group: 'utilities',
      ariaLabel: 'Find and replace text',
    }
  ],
  shortcuts: [
    {
      command: 'find-replace',
      key: 'f',
      ctrlKey: true,
      description: 'Open find and replace dialog'
    }
  ]
};

/**
 * Find and Replace plugin implementation
 * Adds ability to search and replace text in the editor
 */
export class FindReplacePlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private currentMatches: number[] = []; // Store ranges of matches
  private currentMatchIndex = -1;

  constructor() {
    super(config);
  }

  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);

    // Expose plugin globally for debugging
    (window as any).findReplacePlugin = this;
    console.log(`[FindReplace] Plugin initialized and exposed globally`);
  }

  /**
   * Test method to create a simple dialog for debugging
   */
  public testDialog(): void {
    console.log(`[FindReplace] testDialog called`);

    // Create a very simple test dialog
    const testDialog = document.createElement('div');
    testDialog.innerHTML = '<h3>Test Dialog</h3><p>This is a test dialog to verify basic functionality.</p><button onclick="this.parentElement.remove()">Close</button>';
    testDialog.style.position = 'fixed';
    testDialog.style.top = '50px';
    testDialog.style.left = '50px';
    testDialog.style.width = '300px';
    testDialog.style.height = '150px';
    testDialog.style.backgroundColor = 'white';
    testDialog.style.border = '2px solid red';
    testDialog.style.padding = '20px';
    testDialog.style.zIndex = '10000';
    testDialog.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
    testDialog.style.display = 'block';
    testDialog.style.visibility = 'visible';
    testDialog.style.opacity = '1';

    document.body.appendChild(testDialog);
    console.log(`[FindReplace] Test dialog added to DOM`);
  }

  /**
   * Handle theme changes by updating highlight colors
   * @param theme The new theme definition
   */
  protected onThemeChange(theme: any): void {
    super.onThemeChange(theme);

    // Update existing highlights with new theme colors
    if (this.editor) {
      const allHighlights = this.editor.getElement()?.querySelectorAll<HTMLElement>('.js-find-highlight');
      allHighlights?.forEach((highlight) => {
        if (highlight.classList.contains('active')) {
          highlight.style.backgroundColor = 'var(--theme-highlight-active-bg, #fb923c)';
          highlight.style.color = 'var(--theme-highlight-active-text, inherit)';
          highlight.style.outline = '1px solid var(--theme-highlight-active-border, #ea580c)';
        } else {
          highlight.style.backgroundColor = 'var(--theme-highlight-bg, #fef08a)';
          highlight.style.color = 'var(--theme-highlight-text, inherit)';
          highlight.style.outline = '';
        }
      });
    }
  }

  /**
   * Handle the find and replace command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    console.log(`[FindReplace] handleCommand called with: ${_command}`);
    console.log(`[FindReplace] Editor available: ${!!this.editor}`);
    console.log(`[FindReplace] Dialog open: ${this.isDialogOpen}`);

    if (_command === 'find-replace' && this.editor) {
      if (this.isDialogOpen) {
        console.log(`[FindReplace] Closing existing dialog`);
        this.closeFindReplaceDialog();
      } else {
        console.log(`[FindReplace] Opening new dialog`);
        this.openFindReplaceDialog().catch(error => {
          console.error('Failed to open find-replace dialog:', error);
        });
      }
    } else {
      console.log(`[FindReplace] Command ignored - command: ${_command}, editor: ${!!this.editor}`);
    }
  }

  /**
   * Open the find and replace dialog
   */
  private async openFindReplaceDialog(): Promise<void> {
    console.log(`[FindReplace] openFindReplaceDialog called`);
    console.log(`[FindReplace] Dialog already open: ${this.isDialogOpen}`);

    if (this.isDialogOpen) {
      console.log(`[FindReplace] Dialog already open, returning early`);
      return;
    }

    console.log(`[FindReplace] Element factory available: ${!!this.elementFactory}`);
    console.log(`[FindReplace] Theme manager available: ${!!this.themeManager}`);

    // Always create fallback dialog first to ensure we have something
    this.dialog = document.createElement('div');
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'find-replace-dialog-title');

    // Use inline styles for maximum compatibility and visibility
    this.dialog.style.position = 'fixed';
    this.dialog.style.top = '20px';
    this.dialog.style.right = '20px';
    this.dialog.style.width = '320px';
    this.dialog.style.zIndex = '9999';
    this.dialog.style.backgroundColor = '#ffffff';
    this.dialog.style.border = '1px solid #d1d5db';
    this.dialog.style.borderRadius = '8px';
    this.dialog.style.padding = '16px';
    this.dialog.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
    this.dialog.style.display = 'block';
    this.dialog.style.visibility = 'visible';
    this.dialog.style.opacity = '1';
    this.dialog.style.fontFamily = 'system-ui, -apple-system, sans-serif';
    this.dialog.style.fontSize = '14px';
    this.dialog.style.color = '#374151';
    this.dialog.style.lineHeight = '1.5';

    console.log(`[FindReplace] Fallback dialog created as default`);

    // Try to upgrade to themed dialog if possible
    if (this.elementFactory) {
      try {
        console.log(`[FindReplace] Attempting to create themed dialog`);
        const themedDialog = await this.createThemedDialog({
          title: 'Find & Replace',
          modal: false,
          closable: true,
          size: 'medium'
        });

        console.log(`[FindReplace] Themed dialog created successfully, replacing fallback`);
        this.dialog = themedDialog;

        // Position the themed dialog and ensure visibility
        this.dialog.style.position = 'fixed';
        this.dialog.style.top = '20px';
        this.dialog.style.right = '20px';
        this.dialog.style.width = '320px';
        this.dialog.style.zIndex = '9999';
        this.dialog.style.display = 'block';
        this.dialog.style.visibility = 'visible';
        this.dialog.style.opacity = '1';
      } catch (error) {
        console.warn('[FindReplace] Theme factory failed, keeping fallback dialog:', error);
        // Keep the fallback dialog we already created
      }
    } else {
      console.log(`[FindReplace] No element factory available, using fallback dialog`);
    }

    // Create form container
    const form = document.createElement('form');
    form.style.display = 'flex';
    form.style.flexDirection = 'column';
    form.style.gap = '12px';

    // Clear any existing content and add form
    this.dialog.innerHTML = '';

    // Add title
    const title = document.createElement('h3');
    title.id = 'find-replace-dialog-title';
    title.textContent = 'Find & Replace';
    title.style.margin = '0 0 12px 0';
    title.style.fontSize = '16px';
    title.style.fontWeight = '600';
    title.style.color = '#374151';
    this.dialog.appendChild(title);

    // Find Group
    const findGroup = document.createElement('div');
    findGroup.style.display = 'flex';
    findGroup.style.flexDirection = 'column';
    findGroup.style.gap = '6px';

    // Create find input - always use fallback for better control
    const findInputElem = document.createElement('input');
    findInputElem.type = 'text';
    findInputElem.id = 'find-input';
    findInputElem.placeholder = 'Find';
    findInputElem.setAttribute('aria-label', 'Find');
    findInputElem.setAttribute('tabindex', '0');

    // Use inline styles for maximum compatibility
    findInputElem.style.width = '100%';
    findInputElem.style.padding = '8px 12px';
    findInputElem.style.border = '1px solid #d1d5db';
    findInputElem.style.borderRadius = '6px';
    findInputElem.style.fontSize = '14px';
    findInputElem.style.backgroundColor = '#ffffff';
    findInputElem.style.color = '#374151';
    findInputElem.style.outline = 'none';
    findInputElem.style.transition = 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out';

    // Add focus styles via event listeners
    findInputElem.addEventListener('focus', () => {
      findInputElem.style.borderColor = '#3b82f6';
      findInputElem.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
    });

    findInputElem.addEventListener('blur', () => {
      findInputElem.style.borderColor = '#d1d5db';
      findInputElem.style.boxShadow = 'none';
    });

    console.log(`[FindReplace] Created find input element:`, findInputElem);

    // Create wrapper for input and counter
    const findInputWrapper = document.createElement('div');
    findInputWrapper.style.position = 'relative';
    findInputWrapper.style.display = 'flex';
    findInputWrapper.style.alignItems = 'center';
    findInputWrapper.appendChild(findInputElem);

    // Add match counter
    const matchCounterElem = document.createElement('span');
    matchCounterElem.id = 'match-counter';
    matchCounterElem.textContent = '0/0';
    matchCounterElem.style.position = 'absolute';
    matchCounterElem.style.right = '8px';
    matchCounterElem.style.fontSize = '12px';
    matchCounterElem.style.color = '#6b7280';
    matchCounterElem.style.pointerEvents = 'none';
    matchCounterElem.style.backgroundColor = '#ffffff';
    matchCounterElem.style.padding = '2px 4px';
    findInputWrapper.appendChild(matchCounterElem);
    findGroup.appendChild(findInputWrapper);

    const findOptions = document.createElement('div');
    findOptions.style.display = 'flex';
    findOptions.style.gap = '16px';
    findOptions.style.marginTop = '4px';

    const createOption = (id: string, text: string): HTMLLabelElement => {
      const label = document.createElement('label');
      label.style.display = 'flex';
      label.style.alignItems = 'center';
      label.style.gap = '6px';
      label.style.fontSize = '13px';
      label.style.color = '#374151';
      label.style.cursor = 'pointer';

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = id;
      checkbox.style.width = '16px';
      checkbox.style.height = '16px';
      checkbox.style.cursor = 'pointer';

      label.appendChild(checkbox);
      label.appendChild(document.createTextNode(text));
      return label;
    };
    findOptions.appendChild(createOption('case-sensitive-option', 'Case sensitive'));
    findOptions.appendChild(createOption('whole-word-option', 'Whole word'));
    findOptions.appendChild(createOption('regex-option', 'Regex'));
    findGroup.appendChild(findOptions);
    form.appendChild(findGroup);

    // Replace Group
    const replaceGroup = document.createElement('div');
    replaceGroup.style.display = 'flex';
    replaceGroup.style.flexDirection = 'column';
    replaceGroup.style.gap = '6px';

    // Create replace input - always use fallback for better control
    const replaceInputElem = document.createElement('input');
    replaceInputElem.type = 'text';
    replaceInputElem.id = 'replace-input';
    replaceInputElem.placeholder = 'Replace with';
    replaceInputElem.setAttribute('aria-label', 'Replace with');
    replaceInputElem.setAttribute('tabindex', '0');

    // Use inline styles for maximum compatibility
    replaceInputElem.style.width = '100%';
    replaceInputElem.style.padding = '8px 12px';
    replaceInputElem.style.border = '1px solid #d1d5db';
    replaceInputElem.style.borderRadius = '6px';
    replaceInputElem.style.fontSize = '14px';
    replaceInputElem.style.backgroundColor = '#ffffff';
    replaceInputElem.style.color = '#374151';
    replaceInputElem.style.outline = 'none';
    replaceInputElem.style.transition = 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out';

    // Add focus styles via event listeners
    replaceInputElem.addEventListener('focus', () => {
      replaceInputElem.style.borderColor = '#3b82f6';
      replaceInputElem.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
    });

    replaceInputElem.addEventListener('blur', () => {
      replaceInputElem.style.borderColor = '#d1d5db';
      replaceInputElem.style.boxShadow = 'none';
    });

    console.log(`[FindReplace] Created replace input element:`, replaceInputElem);

    const replaceInputWrapper = document.createElement('div');
    replaceInputWrapper.style.position = 'relative';
    replaceInputWrapper.style.display = 'flex';
    replaceInputWrapper.style.alignItems = 'center';
    replaceInputWrapper.appendChild(replaceInputElem);
    replaceGroup.appendChild(replaceInputWrapper);
    form.appendChild(replaceGroup);

    // Actions Group
    const actionsGroup = document.createElement('div');
    actionsGroup.className = 'flex items-center gap-2 mt-2';

    // Navigation buttons
    const navButtons = document.createElement('div');
    navButtons.className = 'flex gap-2';

    // Create navigation buttons using theme-aware factory
    let prevButtonElem: HTMLButtonElement;
    let nextButtonElem: HTMLButtonElement;
    try {
      prevButtonElem = await this.createThemedButton({
        label: '↑',
        variant: 'secondary',
        size: 'small',
        disabled: true
      });
      prevButtonElem.id = 'prev-match-button';
      prevButtonElem.title = 'Previous match';
      prevButtonElem.setAttribute('aria-label', 'Previous match');

      nextButtonElem = await this.createThemedButton({
        label: '↓',
        variant: 'secondary',
        size: 'small',
        disabled: true
      });
      nextButtonElem.id = 'next-match-button';
      nextButtonElem.title = 'Next match';
      nextButtonElem.setAttribute('aria-label', 'Next match');
    } catch (error) {
      // Fallback to manual creation
      console.warn('Theme factory not available for navigation buttons, using fallback:', error);
      prevButtonElem = document.createElement('button');
      prevButtonElem.type = 'button';
      prevButtonElem.id = 'prev-match-button';
      prevButtonElem.className = 'py-1.5 px-3 border border-gray-300 dark:border-slate-600 rounded bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 cursor-pointer text-[13px] text-gray-800 dark:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed';
      prevButtonElem.title = 'Previous match';
      prevButtonElem.setAttribute('aria-label', 'Previous match');
      prevButtonElem.textContent = '↑';
      prevButtonElem.disabled = true;

      nextButtonElem = document.createElement('button');
      nextButtonElem.type = 'button';
      nextButtonElem.id = 'next-match-button';
      nextButtonElem.className = 'py-1.5 px-3 border border-gray-300 dark:border-slate-600 rounded bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 cursor-pointer text-[13px] text-gray-800 dark:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed';
      nextButtonElem.title = 'Next match';
      nextButtonElem.setAttribute('aria-label', 'Next match');
      nextButtonElem.textContent = '↓';
      nextButtonElem.disabled = true;
    }

    navButtons.appendChild(prevButtonElem);
    navButtons.appendChild(nextButtonElem);
    actionsGroup.appendChild(navButtons);

    const flexGrowDiv = document.createElement('div');
    flexGrowDiv.className = 'flex-grow';
    actionsGroup.appendChild(flexGrowDiv);

    // Create replace buttons using theme-aware factory
    let replaceButtonElem: HTMLButtonElement;
    let replaceAllButtonElem: HTMLButtonElement;
    try {
      replaceButtonElem = await this.createThemedButton({
        label: 'Replace',
        variant: 'secondary',
        size: 'small',
        disabled: true
      });
      replaceButtonElem.id = 'replace-button';

      replaceAllButtonElem = await this.createThemedButton({
        label: 'Replace All',
        variant: 'primary',
        size: 'small',
        disabled: true
      });
      replaceAllButtonElem.id = 'replace-all-button';
    } catch (error) {
      // Fallback to manual creation
      console.warn('Theme factory not available for replace buttons, using fallback:', error);
      replaceButtonElem = document.createElement('button');
      replaceButtonElem.type = 'button';
      replaceButtonElem.id = 'replace-button';
      replaceButtonElem.className = 'py-1.5 px-3 border border-gray-300 dark:border-slate-600 rounded bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 cursor-pointer text-[13px] text-gray-800 dark:text-slate-200 disabled:opacity-50 disabled:cursor-not-allowed';
      replaceButtonElem.textContent = 'Replace';
      replaceButtonElem.disabled = true;

      replaceAllButtonElem = document.createElement('button');
      replaceAllButtonElem.type = 'button';
      replaceAllButtonElem.id = 'replace-all-button';
      replaceAllButtonElem.className = 'py-1.5 px-3 rounded cursor-pointer text-[13px] bg-blue-600 hover:bg-blue-700 text-white border border-blue-600 hover:border-blue-700 disabled:opacity-50 disabled:cursor-not-allowed';
      replaceAllButtonElem.textContent = 'Replace All';
      replaceAllButtonElem.disabled = true;
    }

    actionsGroup.appendChild(replaceButtonElem);
    actionsGroup.appendChild(replaceAllButtonElem);
    form.appendChild(actionsGroup);

    this.dialog.appendChild(form);

    // Add dialog to document
    console.log(`[FindReplace] Adding dialog to document body`);
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    console.log(`[FindReplace] Dialog added to DOM, isDialogOpen set to true`);

    // Get form elements
    const findInput = this.dialog.querySelector<HTMLInputElement>('#find-input');
    const replaceInput = this.dialog.querySelector<HTMLInputElement>('#replace-input');
    const caseSensitiveOption = this.dialog.querySelector<HTMLInputElement>('#case-sensitive-option');
    const wholeWordOption = this.dialog.querySelector<HTMLInputElement>('#whole-word-option');
    const regexOption = this.dialog.querySelector<HTMLInputElement>('#regex-option');
    const prevButton = this.dialog.querySelector<HTMLButtonElement>('#prev-match-button');
    const nextButton = this.dialog.querySelector<HTMLButtonElement>('#next-match-button');
    const replaceButton = this.dialog.querySelector<HTMLButtonElement>('#replace-button');
    const replaceAllButton = this.dialog.querySelector<HTMLButtonElement>('#replace-all-button');
    const closeButton = this.dialog.querySelector<HTMLButtonElement>('.feather-find-replace-close');
    const matchCounter = this.dialog.querySelector<HTMLElement>('#match-counter');

    // Setup input events
    findInput?.addEventListener('input', () => {
      this.performSearch(
        findInput.value,
        caseSensitiveOption?.checked ?? false,
        wholeWordOption?.checked ?? false,
        regexOption?.checked ?? false
      );

      // Enable/disable buttons based on whether there are matches
      const hasMatches = this.currentMatches.length > 0;
      if (prevButton) prevButton.disabled = !hasMatches;
      if (nextButton) nextButton.disabled = !hasMatches;
      if (replaceButton) replaceButton.disabled = !hasMatches;
      if (replaceAllButton) replaceAllButton.disabled = !hasMatches;

      // Update match counter
      if (matchCounter) {
        if (hasMatches) {
          matchCounter.textContent = `${this.currentMatchIndex + 1}/${this.currentMatches.length}`;
        } else {
          matchCounter.textContent = '0/0';
        }
      }
    });

    // Setup option changes
    const optionChangeHandler = () => {
      this.performSearch(
        findInput?.value ?? '',
        caseSensitiveOption?.checked ?? false,
        wholeWordOption?.checked ?? false,
        regexOption?.checked ?? false
      );
    };

    caseSensitiveOption?.addEventListener('change', optionChangeHandler);
    wholeWordOption?.addEventListener('change', optionChangeHandler);
    regexOption?.addEventListener('change', optionChangeHandler);

    // Setup navigation buttons
    prevButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateToPreviousMatch();
    });

    nextButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.navigateToNextMatch();
    });

    // Setup replace buttons
    replaceButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.replaceCurrentMatch(replaceInput?.value ?? '');
    });

    replaceAllButton?.addEventListener('click', (e) => {
      e.preventDefault();
      this.replaceAllMatches(replaceInput?.value ?? '');
    });

    // Setup close button
    closeButton?.addEventListener('click', () => {
      this.closeFindReplaceDialog();
    });

    // Handle form submission
    // 'form' is already defined in this scope from programmatic creation
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.navigateToNextMatch();
    });

    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);

    // Get the selected text if any and use it as the initial search term
    const selection = window.getSelection();
    if (selection && !selection.isCollapsed && selection.toString().trim()) {
      const selectedText = selection.toString().trim();
      if (findInput) findInput.value = selectedText;
      this.performSearch(
        selectedText,
        caseSensitiveOption?.checked ?? false,
        wholeWordOption?.checked ?? false,
        regexOption?.checked ?? false
      );
    }

    // Focus the search input
    findInput?.focus();
    findInput?.select();

    console.log(`[FindReplace] Dialog setup complete`);
    console.log(`[FindReplace] Dialog element:`, this.dialog);
    console.log(`[FindReplace] Dialog in DOM:`, document.body.contains(this.dialog));
    console.log(`[FindReplace] isDialogOpen:`, this.isDialogOpen);
  }

  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeFindReplaceDialog();
    }
  };

  /**
   * Close the find and replace dialog
   */
  private closeFindReplaceDialog(): void {
    if (!this.isDialogOpen || !this.dialog) return;

    // Remove highlights
    this.removeAllHighlights();

    // Remove dialog from DOM
    this.dialog.remove();
    this.dialog = null;
    this.isDialogOpen = false;

    // Restore editor focus and selection
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.focus();
      // Restore selection (if needed, implement restoration logic)
      // const savedSelection = this.dialog.dataset.restoreSelection;
      // if (savedSelection) {
      //   const selection = window.getSelection();
      //   selection?.removeAllRanges();
      //   selection?.addRange(JSON.parse(savedSelection));
      // }
    }

    // Reset state
    this.currentMatches = [];
    this.currentMatchIndex = -1;
  }

  /**
   * Perform a search in the editor content
   * @param searchText The text to search for
   * @param caseSensitive Whether the search is case sensitive
   * @param wholeWord Whether to match whole words only
   * @param isRegex Whether the search text is a regular expression
   */
  private performSearch(
    searchText: string,
    caseSensitive: boolean = false,
    wholeWord: boolean = false,
    isRegex: boolean = false
  ): void {
    if (!this.editor || !searchText) {
      this.removeAllHighlights();
      this.currentMatches = [];
      this.currentMatchIndex = -1;
      this.updateMatchCounter();
      this.updateActionButtons();
      return;
    }

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    this.removeAllHighlights();
    this.currentMatches = [];
    this.currentMatchIndex = -1;

    try {
      // Create the search pattern
      let pattern: RegExp;

      if (isRegex) {
        // User-provided regex
        const flags = caseSensitive ? 'g' : 'gi';
        pattern = new RegExp(searchText, flags);
      } else {
        // Escape special regex characters if not in regex mode
        const escapedText = searchText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const wordBoundary = wholeWord ? '\\b' : '';
        const flags = caseSensitive ? 'g' : 'gi';
        pattern = new RegExp(`${wordBoundary}${escapedText}${wordBoundary}`, flags);
      }

      // Get editor content as text
      const editorContent = editorElement.textContent ?? '';

      // Find all matches
      const matches: number[] = [];
      let match;

      while ((match = pattern.exec(editorContent)) !== null) {
        matches.push(match.index);
      }

      this.currentMatches = matches;

      // Highlight all matches
      this.highlightMatches();

      // Navigate to first match if there are any
      if (matches.length > 0) {
        this.navigateToMatch(0);
      }
    } catch (error) {
      console.error('Search error:', error);
      // Handle invalid regex
      if (isRegex && error instanceof SyntaxError) {
        // Could show an error message to the user here
      }
    }
  }

  /**
   * Highlight all matches in the editor
   */
  private highlightMatches(): void {
    if (!this.editor) return;
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    const selection = window.getSelection();
    if (!selection) return;

    // Create a document fragment to avoid multiple reflows
    const fragment = document.createDocumentFragment();
    let lastIndex = 0;

    // Process each match
    this.currentMatches.forEach((matchIndex, i) => {
      // Add text before the match
      if (matchIndex > lastIndex) {
        fragment.appendChild(document.createTextNode(editorElement.textContent?.substring(lastIndex, matchIndex) ?? ''));
      }

      // Get the match text (assume it's one character for simplicity)
      // In a real implementation, you'd need to know the length of each match
      const matchLength = 1; // This would need to be calculated from the actual match
      const matchText = editorElement.textContent?.substr(matchIndex, matchLength) ?? ''; // Handle potential null/undefined

      // Create a span for the highlighted match
      const highlightSpan = document.createElement('span');
      // Use theme-aware CSS variables for highlighting
      highlightSpan.className = 'js-find-highlight rounded-sm';
      highlightSpan.style.backgroundColor = 'var(--theme-highlight-bg, #fef08a)';
      highlightSpan.style.color = 'var(--theme-highlight-text, inherit)';
      highlightSpan.setAttribute('data-match-index', i.toString());
      highlightSpan.textContent = matchText;

      fragment.appendChild(highlightSpan);

      lastIndex = matchIndex + matchLength;
    });

    // Add any remaining text
    const editorTextContent = editorElement.textContent ?? ''; // Handle null
    if (lastIndex < editorTextContent.length) { // Use safe length access
      fragment.appendChild(document.createTextNode(editorTextContent.substring(lastIndex))); // Use safe substring
    }

    // Replace the editor content
    editorElement.innerHTML = '';
    editorElement.appendChild(fragment);
  }

  /**
   * Remove all highlight spans from the editor
   */
  private removeAllHighlights(): void {
    if (!this.editor) return;
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Get all highlight spans using the JS hook class
    const highlights = editorElement.querySelectorAll<HTMLElement>('.js-find-highlight');

    // Replace each highlight with its text content
    highlights.forEach((highlight) => {
      const parent = highlight.parentNode;
      if (parent) {
        parent.replaceChild(document.createTextNode(highlight.textContent ?? ''), highlight); // Handle potential null/undefined
        parent.normalize(); // Merge adjacent text nodes
      }
    });
  }

  /**
   * Navigate to a specific match
   * @param index The index of the match to navigate to
   */
  private navigateToMatch(index: number): void {
    if (!this.editor || this.currentMatches.length === 0) return;

    // Ensure the index is within bounds
    index = Math.max(0, Math.min(index, this.currentMatches.length - 1));

    // Update the current match index
    this.currentMatchIndex = index;

    // Remove active styling from all highlights and ensure base styling
    const allHighlights = this.editor.getElement()?.querySelectorAll<HTMLElement>('.js-find-highlight');
    allHighlights?.forEach((highlight) => {
      highlight.classList.remove('active');
      highlight.style.backgroundColor = 'var(--theme-highlight-bg, #fef08a)';
      highlight.style.color = 'var(--theme-highlight-text, inherit)';
      highlight.style.outline = '';
    });

    // Add active styling to the current match
    const currentHighlight = this.editor.getElement()?.querySelector<HTMLElement>(`.js-find-highlight[data-match-index="${index}"]`);

    if (currentHighlight) {
      currentHighlight.classList.add('active');
      currentHighlight.style.backgroundColor = 'var(--theme-highlight-active-bg, #fb923c)';
      currentHighlight.style.color = 'var(--theme-highlight-active-text, inherit)';
      currentHighlight.style.outline = '1px solid var(--theme-highlight-active-border, #ea580c)';

      // Scroll the highlight into view
      currentHighlight.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // Update the match counter
      if (this.dialog) {
        const matchCounter = this.dialog.querySelector<HTMLElement>('#match-counter');
        if (matchCounter) {
          matchCounter.textContent = `${index + 1}/${this.currentMatches.length}`;
        }
      }
    } else {
      console.warn(`Highlight for match index ${index} not found.`);
      // Potentially re-run search if highlights are out of sync
    }
  }

  /**
   * Navigate to the next match
   */
  private navigateToNextMatch(): void {
    if (this.currentMatches.length === 0) return;

    let nextIndex = this.currentMatchIndex + 1;
    if (nextIndex >= this.currentMatches.length) {
      nextIndex = 0; // Wrap around to the first match
    }

    this.navigateToMatch(nextIndex);
  }

  /**
   * Navigate to the previous match
   */
  private navigateToPreviousMatch(): void {
    if (this.currentMatches.length === 0) return;

    let prevIndex = this.currentMatchIndex - 1;
    if (prevIndex < 0) {
      prevIndex = this.currentMatches.length - 1; // Wrap around to the last match
    }

    this.navigateToMatch(prevIndex);
  }

  /**
   * Replace the current match with the replacement text
   * @param replaceText The text to replace the match with
   */
  private replaceCurrentMatch(replaceText: string): void {
    if (!this.editor || this.currentMatches.length === 0 || this.currentMatchIndex < 0) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Get the current highlighted match
    const currentHighlight = editorElement.querySelector<HTMLElement>(`.js-find-highlight.active`);

    if (currentHighlight) {
      // Create a text node with the replacement text
      const textNode = document.createTextNode(replaceText);

      // Replace the highlight with the text node
      currentHighlight.parentNode?.replaceChild(textNode, currentHighlight);

      // Trigger input event for history
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );

      // Re-run the search to update matches after replacement
      const findInput = this.dialog?.querySelector<HTMLInputElement>('#find-input');
      const caseSensitiveOption = this.dialog?.querySelector<HTMLInputElement>('#case-sensitive-option');
      const wholeWordOption = this.dialog?.querySelector<HTMLInputElement>('#whole-word-option');
      const regexOption = this.dialog?.querySelector<HTMLInputElement>('#regex-option');

      if (findInput) {
        // Get the new index before performing search, as search resets it
        const nextIndex = this.currentMatchIndex;

        this.performSearch(
          findInput.value,
          caseSensitiveOption?.checked ?? false,
          wholeWordOption?.checked ?? false,
          regexOption?.checked ?? false
        );

        // Try to navigate to the position where the replacement happened
        if (nextIndex < this.currentMatches.length) {
            this.navigateToMatch(nextIndex);
        } else if (this.currentMatches.length > 0) {
            // If the replaced item was the last one, go to the new last one
            this.navigateToMatch(this.currentMatches.length - 1);
        } // Otherwise, search found nothing, state is already reset
      }
    } else {
        console.warn('Could not find the active highlight to replace.');
    }
  }

  /**
   * Replace all matches with the replacement text
   * @param replaceText The text to replace all matches with
   */
  private replaceAllMatches(replaceText: string): void {
    if (!this.editor || this.currentMatches.length === 0) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Get all highlight spans (use a static NodeList) using the JS hook class
    const highlights = editorElement.querySelectorAll<HTMLElement>('.js-find-highlight');

    // Replace each highlight with the replacement text
    // Iterate backwards to avoid issues with NodeList changing during iteration
    for (let i = highlights.length - 1; i >= 0; i--) {
        const highlight = highlights[i];
        const textNode = document.createTextNode(replaceText);
        highlight.parentNode?.replaceChild(textNode, highlight);
    }

    // Normalize the container to merge adjacent text nodes
    editorElement.normalize();

    // Trigger input event for history
    editorElement.dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );

    // Reset match state
    this.currentMatches = [];
    this.currentMatchIndex = -1;
  }

  /**
   * Updates the text content of the match counter element.
   */
  private updateMatchCounter(): void {
    const matchCounter = this.dialog?.querySelector<HTMLElement>('#match-counter');
    if (!matchCounter) return;

    const hasMatches = this.currentMatches.length > 0;
    if (hasMatches) {
      matchCounter.textContent = `${this.currentMatchIndex + 1}/${this.currentMatches.length}`;
    } else {
      matchCounter.textContent = '0/0';
    }
  }

  /**
   * Enables or disables action buttons based on the current search state.
   */
  private updateActionButtons(): void {
    const prevButton = this.dialog?.querySelector<HTMLButtonElement>('#prev-match-button');
    const nextButton = this.dialog?.querySelector<HTMLButtonElement>('#next-match-button');
    const replaceButton = this.dialog?.querySelector<HTMLButtonElement>('#replace-button');
    const replaceAllButton = this.dialog?.querySelector<HTMLButtonElement>('#replace-all-button');

    const hasMatches = this.currentMatches.length > 0;
    const canReplace = hasMatches && this.currentMatchIndex !== -1;

    if (prevButton) prevButton.disabled = !hasMatches;
    if (nextButton) nextButton.disabled = !hasMatches;
    if (replaceButton) replaceButton.disabled = !canReplace;
    if (replaceAllButton) replaceAllButton.disabled = !hasMatches;
  }

  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();

    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeFindReplaceDialog();
    }

    // Remove potential leftover style tag (though it should be external now)
    const styleTag = document.getElementById('feather-find-replace-styles');
    styleTag?.remove();
  }
}

// Create and export the plugin instance directly
const plugin = new FindReplacePlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
