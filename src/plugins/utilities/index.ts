import type { Plugin } from '../../types';

// Import all utility plugins
import historyControlsPlugin from './history-controls';
import findReplacePlugin from './find-replace';
import wordCountPlugin from './word-count';
import sourceViewPlugin from './source-view';
import fullscreenPlugin from './fullscreen';
import printExportPlugin from './print-export';
import importExportPlugin from './import-export';
import spellGrammarPlugin from './spell-grammar';
import markdownTogglePlugin from './markdown-toggle';

// Export individual plugins for direct imports
export {
  historyControlsPlugin,
  findReplacePlugin,
  wordCountPlugin,
  sourceViewPlugin,
  fullscreenPlugin,
  printExportPlugin,
  importExportPlugin,
  spellGrammarPlugin,
  markdownTogglePlugin
}

// Export a combined plugin group for convenience
export const UtilityPlugins: Plugin[] = [
  historyControlsPlugin,
  findReplacePlugin,
  wordCountPlugin,
  sourceViewPlugin,
  fullscreenPlugin,
  printExportPlugin,
  importExportPlugin,
  spellGrammarPlugin,
  markdownTogglePlugin
];

// Export the utility plugins array directly
// PluginManager will handle individual plugin registration
export default UtilityPlugins;