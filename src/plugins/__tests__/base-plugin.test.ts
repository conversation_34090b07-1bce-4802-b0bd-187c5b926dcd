/**
 * Enhanced BasePlugin Tests
 *
 * Comprehensive unit tests for the enhanced BasePlugin class
 * covering theme integration, element creation utilities, and backward compatibility
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { BasePlugin, PluginConfig } from '../base-plugin';
import { SemanticRole } from '../../themes/element-factory/interfaces';
import { DARK_THEME } from '../../themes/theme-config';
import type { Editor } from '../../types';

// Mock implementations
class TestPlugin extends BasePlugin {
  public handleCommand(command: string): void {
    // Test implementation
    console.log(`Handling command: ${command}`);
  }
}

// Mock editor
const mockEditor: Editor = {
  focus: vi.fn(),
  // Add other required editor properties as needed
} as any;

// Mock theme manager
const mockThemeManager = {
  watch: vi.fn(),
  getCurrentTheme: vi.fn(),
  setTheme: vi.fn()
} as any;

// Mock element factory
const mockElementFactory = {
  createButton: vi.fn(),
  createDialog: vi.fn(),
  createInput: vi.fn(),
  createPanel: vi.fn(),
  createElement: vi.fn(),
  applyThemeToElement: vi.fn(),
  removeThemeFromElement: vi.fn()
} as any;

describe('Enhanced BasePlugin', () => {
  let plugin: TestPlugin;
  let config: PluginConfig;

  beforeEach(() => {
    // Reset DOM
    document.body.innerHTML = '<div id="toolbar"></div>';

    // Reset window globals
    (window as any).featherThemeManager = undefined;
    (window as any).featherElementFactory = undefined;

    // Reset mocks
    vi.clearAllMocks();

    config = {
      id: 'test-plugin',
      name: 'Test Plugin',
      description: 'A test plugin',
      toolbarItems: [
        {
          id: 'test-button',
          command: 'test-command',
          icon: '🧪',
          label: 'Test',
          tooltip: 'Test button'
        }
      ],
      shortcuts: [
        {
          command: 'test-shortcut',
          key: 't',
          ctrlKey: true
        }
      ]
    };

    plugin = new TestPlugin(config);
  });

  afterEach(() => {
    plugin.destroy();
    document.body.innerHTML = '';
  });

  describe('Basic Plugin Functionality', () => {
    it('should initialize without theme integration', () => {
      expect(() => plugin.init(mockEditor)).not.toThrow();
      expect(plugin['editor']).toBe(mockEditor);
    });

    it('should create standard toolbar buttons when theme integration is not available', async () => {
      plugin.init(mockEditor);

      // Wait for async toolbar registration
      await new Promise(resolve => setTimeout(resolve, 10));

      const toolbar = document.getElementById('toolbar');
      const buttons = toolbar?.querySelectorAll('button');

      expect(buttons).toHaveLength(1);
      expect(buttons?.[0].getAttribute('data-command')).toBe('test-command');
      expect(buttons?.[0].getAttribute('aria-label')).toBe('Test');
    });

    it('should register keyboard shortcuts', () => {
      const addEventListenerSpy = vi.spyOn(document, 'addEventListener');

      plugin.init(mockEditor);

      expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));
    });

    it('should clean up resources on destroy', () => {
      plugin.init(mockEditor);

      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');

      plugin.destroy();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));

      const toolbar = document.getElementById('toolbar');
      const buttons = toolbar?.querySelectorAll('button');
      expect(buttons).toHaveLength(0);
    });
  });

  describe('Theme Integration', () => {
    beforeEach(() => {
      // Set up theme integration mocks
      (window as any).featherThemeManager = mockThemeManager;
      (window as any).featherElementFactory = mockElementFactory;

      mockThemeManager.watch.mockReturnValue(() => {}); // Unsubscribe function
      mockElementFactory.createButton.mockResolvedValue(document.createElement('button'));
    });

    it('should initialize theme integration when available', () => {
      plugin.init(mockEditor);

      expect(plugin['themeManager']).toBe(mockThemeManager);
      expect(plugin['elementFactory']).toBe(mockElementFactory);
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should create themed toolbar buttons when theme integration is available', async () => {
      const themedButton = document.createElement('button');
      themedButton.setAttribute('data-themed', 'true');
      mockElementFactory.createButton.mockResolvedValue(themedButton);

      plugin.init(mockEditor);

      // Wait for async toolbar registration
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockElementFactory.createButton).toHaveBeenCalledWith({
        label: 'Test',
        icon: '🧪',
        variant: 'secondary',
        onClick: expect.any(Function)
      });

      const toolbar = document.getElementById('toolbar');
      const buttons = toolbar?.querySelectorAll('button');
      expect(buttons?.[0].getAttribute('data-themed')).toBe('true');
    });

    it('should fallback to standard buttons if themed button creation fails', async () => {
      mockElementFactory.createButton.mockRejectedValue(new Error('Theme creation failed'));

      plugin.init(mockEditor);

      // Wait for async toolbar registration
      await new Promise(resolve => setTimeout(resolve, 10));

      const toolbar = document.getElementById('toolbar');
      const buttons = toolbar?.querySelectorAll('button');

      expect(buttons).toHaveLength(1);
      expect(buttons?.[0].getAttribute('data-themed')).toBeNull();
    });

    it('should handle theme changes', () => {
      plugin.init(mockEditor);

      const themeChangeCallback = mockThemeManager.watch.mock.calls[0][0];

      // Should not throw when theme changes
      expect(() => themeChangeCallback(DARK_THEME)).not.toThrow();
    });

    it('should clean up theme integration on destroy', () => {
      const unsubscribeMock = vi.fn();
      mockThemeManager.watch.mockReturnValue(unsubscribeMock);

      plugin.init(mockEditor);
      plugin.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
      expect(plugin['themeManager']).toBeUndefined();
      expect(plugin['elementFactory']).toBeUndefined();
    });
  });

  describe('Theme Utility Methods', () => {
    beforeEach(() => {
      (window as any).featherThemeManager = mockThemeManager;
      (window as any).featherElementFactory = mockElementFactory;
      mockThemeManager.watch.mockReturnValue(() => {}); // Unsubscribe function
      plugin.init(mockEditor);
    });

    it('should create themed buttons', async () => {
      const mockButton = document.createElement('button');
      mockElementFactory.createButton.mockResolvedValue(mockButton);

      const button = await plugin['createThemedButton']({
        label: 'Test Button',
        variant: 'primary'
      });

      expect(mockElementFactory.createButton).toHaveBeenCalledWith({
        label: 'Test Button',
        variant: 'primary'
      });
      expect(button).toBe(mockButton);
    });

    it('should create themed dialogs', async () => {
      const mockDialog = document.createElement('dialog');
      mockElementFactory.createDialog.mockResolvedValue(mockDialog);

      const dialog = await plugin['createThemedDialog']({
        title: 'Test Dialog',
        content: 'Test content'
      });

      expect(mockElementFactory.createDialog).toHaveBeenCalledWith({
        title: 'Test Dialog',
        content: 'Test content'
      });
      expect(dialog).toBe(mockDialog);
    });

    it('should throw error when element factory is not available', async () => {
      plugin['elementFactory'] = undefined;

      await expect(plugin['createThemedButton']({ label: 'Test' }))
        .rejects.toThrow('Element factory not available');
    });

    it('should apply theme to existing elements', async () => {
      const element = document.createElement('div');

      await plugin['applyThemeToElement'](element, SemanticRole.BUTTON);

      expect(mockElementFactory.applyThemeToElement).toHaveBeenCalledWith(
        element,
        SemanticRole.BUTTON,
        undefined
      );
      expect(plugin['themeAwareElements']).toContain(element);
    });
  });

  describe('Backward Compatibility', () => {
    it('should work without theme integration', async () => {
      // No theme manager or element factory available
      expect(() => plugin.init(mockEditor)).not.toThrow();

      // Wait for async toolbar registration
      await new Promise(resolve => setTimeout(resolve, 10));

      // Should still create standard toolbar buttons
      const toolbar = document.getElementById('toolbar');
      const buttons = toolbar?.querySelectorAll('button');
      expect(buttons).toHaveLength(1);
    });

    it('should handle theme integration failures gracefully', () => {
      (window as any).featherThemeManager = { watch: () => { throw new Error('Theme error'); } };

      expect(() => plugin.init(mockEditor)).not.toThrow();
    });
  });
});
