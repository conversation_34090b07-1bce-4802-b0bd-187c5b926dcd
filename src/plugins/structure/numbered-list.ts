import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Numbered list plugin configuration
 */
const config: PluginConfig = {
  id: 'numbered-list',
  name: 'Numbered List',
  description: 'Create and format numbered lists',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'numbered-list',
      command: 'numberedList',
      icon: '1.',
      label: 'Numbered List',
      tooltip: 'Numbered List (Ctrl/⌘+Shift+7)',
      group: 'structure',
      ariaLabel: 'Create numbered list',
    }
  ],
  shortcuts: [
    {
      command: 'numberedList',
      key: '7',
      ctrlKey: true,
      shiftKey: true,
      description: 'Create numbered list'
    }
  ]
};

/**
 * Numbered list plugin implementation
 * Creates and manages ordered lists
 */
export class NumberedListPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the numbered list command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    if (_command === 'numberedList' && this.editor) {
      this.toggleNumberedList();
    }
  }
  
  /**
   * Toggle numbered list formatting for the current selection or block
   */
  private toggleNumberedList(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Find the current context - are we already in a list?
    const listContext = this.getListContext(range);
    
    if (listContext.inNumberedList) {
      // If we're already in a numbered list, convert it back to normal paragraphs
      this.convertListToParagraphs(listContext.listElement);
    } else if (listContext.inBulletList) {
      // If we're in a bullet list, convert it to a numbered list
      this.convertBulletToNumberedList(listContext.listElement);
    } else {
      // Otherwise, create a new numbered list
      this.createNumberedList(range);
    }
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Get the current list context based on a selection range
   * @param range The current selection range
   * @returns Information about the current list context
   */
  private getListContext(range: Range): { 
    inBulletList: boolean; 
    inNumberedList: boolean; 
    listElement: HTMLElement | null;
  } {
    let container = range.commonAncestorContainer;
    
    // Navigate up to element node if we're in a text node
    if (container.nodeType === Node.TEXT_NODE) {
      container = container.parentElement as Node;
    }
    
    // Find the containing list, if any
    let listItem: Element | null = null;
    let listElement: HTMLElement | null = null;
    
    if (container instanceof HTMLElement) {
      listItem = container.closest('li');
      if (listItem) {
        listElement = listItem.closest('ul, ol') as HTMLElement;
      }
    }
    
    return {
      inBulletList: !!listElement && listElement.tagName === 'UL',
      inNumberedList: !!listElement && listElement.tagName === 'OL',
      listElement
    };
  }
  
  /**
   * Create a new numbered list from the current selection
   * @param range The current selection range
   */
  private createNumberedList(range: Range): void {
    if (!this.editor) return;
    
    // Find the block elements in the selection
    const blocks = this.getBlocksInRange(range);
    if (blocks.length === 0) return;
    
    // Create a new list element
    const olElement = document.createElement('ol');
    
    // Process each block
    blocks.forEach(block => {
      const li = document.createElement('li');
      li.innerHTML = block.innerHTML;
      olElement.appendChild(li);
      
      // Remove the original block
      if (block.parentNode) {
        block.parentNode.removeChild(block);
      }
    });
    
    // Insert the new list at the start position of the first block
    const firstBlock = blocks[0];
    if (firstBlock.parentNode) {
      firstBlock.parentNode.insertBefore(olElement, firstBlock);
    } else {
      // Fallback - append to editor
      this.editor.getElement().appendChild(olElement);
    }
    
    // Set selection to the first list item
    const firstLi = olElement.firstElementChild;
    if (firstLi) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        const newRange = document.createRange();
        newRange.selectNodeContents(firstLi);
        selection.addRange(newRange);
      }
    }
  }
  
  /**
   * Convert a list to normal paragraphs
   * @param listElement The list element to convert
   */
  private convertListToParagraphs(listElement: HTMLElement | null): void {
    if (!listElement || !listElement.parentNode) return;
    
    // Get all list items
    const items = Array.from(listElement.querySelectorAll('li'));
    
    // Create a document fragment to hold the paragraphs
    const fragment = document.createDocumentFragment();
    
    // Convert each list item to a paragraph
    items.forEach(item => {
      const p = document.createElement('p');
      p.innerHTML = item.innerHTML;
      fragment.appendChild(p);
    });
    
    // Replace the list with the new paragraphs
    listElement.parentNode.replaceChild(fragment, listElement);
  }
  
  /**
   * Convert a bullet list to a numbered list
   * @param listElement The bullet list element to convert
   */
  private convertBulletToNumberedList(listElement: HTMLElement | null): void {
    if (!listElement || listElement.tagName !== 'UL' || !listElement.parentNode) return;
    
    // Create a new numbered list
    const olElement = document.createElement('ol');
    
    // Copy all the list items
    Array.from(listElement.children).forEach(child => {
      olElement.appendChild(child.cloneNode(true));
    });
    
    // Replace the bullet list with the numbered list
    listElement.parentNode.replaceChild(olElement, listElement);
  }
  
  /**
   * Get block level elements in the current selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      // If we didn't find a block element, create a paragraph around the selection
      const paragraph = document.createElement('p');
      range.surroundContents(paragraph);
      return [paragraph];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
}

// Create and export the plugin instance directly
const plugin = new NumberedListPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
