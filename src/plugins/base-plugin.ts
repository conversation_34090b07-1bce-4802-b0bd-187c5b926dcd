import type { Editor, Plugin } from '../types';
import { ThemeManager } from '../themes/theme-manager';
import { ThemeElementFactory } from '../themes/element-factory';
import { SemanticRole, ElementOptions } from '../themes/element-factory/interfaces';
import type { ThemeDefinition } from '../themes/theme-types';

/**
 * Base interface for all FeatherJS plugins
 * Ensures consistent API across all plugins
 */
export interface PluginConfig {
  id: string;
  name: string;
  description?: string;
  version?: string;
  toolbarItems?: ToolbarItemConfig[];
  shortcuts?: ShortcutConfig[];
}

/**
 * Configuration for toolbar items
 */
export interface ToolbarItemConfig {
  id: string;
  command: string;
  icon: string;
  label: string;
  tooltip?: string;
  group?: string;
  position?: number;
  ariaLabel?: string;
  ariaRole?: string;
  component?: string;
  type?: string;
}

/**
 * Configuration for keyboard shortcuts
 */
export interface ShortcutConfig {
  command: string;
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  preventDefault?: boolean;
  description?: string;
}

/**
 * Base class for all FeatherJS plugins
 * Provides common functionality and ensures consistent API
 * Enhanced with theme integration capabilities for automatic theme-aware element creation
 * Implements the Plugin interface to ensure compatibility
 */
export abstract class BasePlugin implements Plugin {
  protected editor?: Editor;
  protected config: PluginConfig;
  protected shortcutHandlers: { [key: string]: (event: KeyboardEvent) => void } = {};

  // Theme integration properties
  protected themeManager?: ThemeManager;
  protected elementFactory?: ThemeElementFactory;
  protected themeChangeUnsubscribe?: () => void;
  protected themeAwareElements: HTMLElement[] = [];
  protected commandListener?: (event: Event) => void;

  constructor(config: PluginConfig) {
    this.config = config;
  }

  /**
   * Get the plugin ID from the config
   * Required by Plugin interface
   */
  get id(): string {
    return this.config.id;
  }

  /**
   * Get toolbar items from the config
   * Required by Plugin interface
   */
  get toolbarItems() {
    return this.config.toolbarItems?.map(item => ({
      id: item.id,
      command: item.command,
      label: item.label,
      icon: item.icon,
      title: item.tooltip,
      ariaLabel: item.ariaLabel,
      ariaRole: item.ariaRole,
      group: item.group,
      position: item.position,
      component: item.component,
      type: item.type
    }));
  }

  /**
   * Initialize the plugin with the editor instance
   * Enhanced with automatic theme integration setup
   * @param editor The editor instance
   */
  init(editor: Editor): void {
    this.editor = editor;

    // Initialize theme integration
    this.initializeThemeIntegration();

    // Note: Toolbar items are now handled by ToolbarLayoutManager
    // No need to register them here as it would create duplicates

    // Listen for toolbar command events
    this.registerCommandListener();

    this.registerShortcuts();
    this.onInit();
  }

  /**
   * Initialize theme integration for the plugin
   * Sets up theme manager, element factory, and theme change listeners
   * @protected
   */
  protected initializeThemeIntegration(): void {
    try {
      // Get global theme manager instance
      this.themeManager = (window as any).featherThemeManager;

      if (this.themeManager) {
        // Get element factory from global instance
        this.elementFactory = (window as any).featherElementFactory;

        // Set up theme change listener
        this.themeChangeUnsubscribe = this.themeManager.watch((theme: ThemeDefinition) => {
          this.onThemeChange(theme);
        });
      }
    } catch (error) {
      console.warn(`Theme integration failed for plugin ${this.config.id}:`, error);
      // Plugin should continue to work without theme integration
    }
  }

  /**
   * Register command listener for toolbar events
   * @protected
   */
  protected registerCommandListener(): void {
    this.commandListener = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { command, pluginId } = customEvent.detail || {};

      console.log(`[BasePlugin:${this.config.id}] Received feather:command event:`, { command, pluginId, source: customEvent.detail?.source });

      // Only handle commands for this plugin
      if (pluginId === this.config.id) {
        console.log(`[BasePlugin:${this.config.id}] Handling command: ${command}`);
        this.handleCommand(command);
        if (this.editor) {
          this.editor.focus();
        }
      } else {
        console.log(`[BasePlugin:${this.config.id}] Ignoring command for different plugin: ${pluginId}`);
      }
    };

    document.addEventListener('feather:command', this.commandListener);
    console.log(`[BasePlugin:${this.config.id}] Command listener registered`);
  }

  /**
   * DEPRECATED: Toolbar registration is now handled by ToolbarLayoutManager
   * This method is kept for backward compatibility but does nothing
   * All toolbar items should be defined in the plugin config and will be
   * automatically rendered by ToolbarLayoutManager based on the toolbar layout
   * @deprecated Use ToolbarLayoutManager for all toolbar rendering
   * @protected
   */
  protected async registerToolbarItems(): Promise<void> {
    // No-op: ToolbarLayoutManager handles all toolbar rendering
    // This eliminates the dual button creation conflict
  }

  // ===== Theme-Aware Element Creation Utilities =====

  /**
   * Create a themed button element using the element factory
   * @protected
   */
  protected async createThemedButton(options: {
    label: string;
    variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    icon?: string;
    onClick?: () => void;
  }): Promise<HTMLButtonElement> {
    if (!this.elementFactory) {
      throw new Error('Element factory not available');
    }

    return await this.elementFactory.createButton(options);
  }

  /**
   * Create a themed dialog element using the element factory
   * @protected
   */
  protected async createThemedDialog(options: {
    title?: string;
    content?: string | HTMLElement;
    actions?: Array<{
      label: string;
      action: () => void;
      variant?: 'primary' | 'secondary' | 'danger';
    }>;
    modal?: boolean;
    closable?: boolean;
    size?: 'small' | 'medium' | 'large';
  }): Promise<HTMLDialogElement> {
    if (!this.elementFactory) {
      throw new Error('Element factory not available');
    }

    return await this.elementFactory.createDialog(options);
  }

  /**
   * Create a themed input element using the element factory
   * @protected
   */
  protected async createThemedInput(options: {
    type?: string;
    placeholder?: string;
    label?: string;
    value?: string;
    required?: boolean;
    disabled?: boolean;
    validation?: {
      pattern?: string;
      minLength?: number;
      maxLength?: number;
      customValidator?: (value: string) => boolean;
    };
    onChange?: (value: string) => void;
  }): Promise<HTMLInputElement> {
    if (!this.elementFactory) {
      throw new Error('Element factory not available');
    }

    return await this.elementFactory.createInput(options);
  }

  /**
   * Create a themed panel/card element using the element factory
   * @protected
   */
  protected async createThemedPanel(options: {
    title?: string;
    content?: string | HTMLElement;
    collapsible?: boolean;
    collapsed?: boolean;
    variant?: 'default' | 'elevated' | 'outlined';
    padding?: 'none' | 'small' | 'medium' | 'large';
  }): Promise<HTMLDivElement> {
    if (!this.elementFactory) {
      throw new Error('Element factory not available');
    }

    return await this.elementFactory.createPanel(options);
  }

  /**
   * Create a custom themed element using the element factory
   * @protected
   */
  protected async createThemedElement<T extends HTMLElement = HTMLElement>(
    options: ElementOptions<T>
  ): Promise<T> {
    if (!this.elementFactory) {
      throw new Error('Element factory not available');
    }

    const element = await this.elementFactory.createElement(options);
    this.themeAwareElements.push(element);
    return element;
  }

  /**
   * Apply theme to an existing element
   * @protected
   */
  protected async applyThemeToElement(
    element: HTMLElement,
    role: SemanticRole,
    themeOverride?: Partial<ThemeDefinition>
  ): Promise<void> {
    if (!this.elementFactory) {
      console.warn('Element factory not available, cannot apply theme');
      return;
    }

    await this.elementFactory.applyThemeToElement(element, role, themeOverride);
    this.themeAwareElements.push(element);
  }

  /**
   * Hook called when theme changes
   * Override this method to handle theme changes in your plugin
   * @protected
   */
  protected onThemeChange(_theme: ThemeDefinition): void {
    // Override in subclasses to handle theme changes
    // This method is called automatically when the theme changes
  }

  /**
   * Register keyboard shortcuts for this plugin
   * @protected
   */
  protected registerShortcuts(): void {
    this.config.shortcuts?.forEach(shortcut => {
      const handler = (event: KeyboardEvent) => {
        // Check if the key combination matches
        const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
        const ctrlMatches = Boolean(event.ctrlKey) === Boolean(shortcut.ctrlKey);
        const altMatches = Boolean(event.altKey) === Boolean(shortcut.altKey);
        const shiftMatches = Boolean(event.shiftKey) === Boolean(shortcut.shiftKey);
        const metaMatches = Boolean(event.metaKey) === Boolean(shortcut.metaKey);

        if (keyMatches && ctrlMatches && altMatches && shiftMatches && metaMatches) {
          if (shortcut.preventDefault !== false) {
            event.preventDefault();
          }
          this.handleCommand(shortcut.command);
        }
      };

      this.shortcutHandlers[shortcut.command] = handler;
      document.addEventListener('keydown', handler);
    });
  }

  /**
   * Handle command events dispatched by the toolbar or shortcuts
   * To be implemented by subclasses
   * Made public to satisfy Plugin interface
   * @param _command The command to handle
   */
  public abstract handleCommand(_command: string): void;

  /**
   * Called when the plugin is initialized
   * Override this method to add custom initialization logic
   * @protected
   */
  protected onInit(): void {
    // Override in subclasses
  }

  /**
   * Clean up resources when the plugin is destroyed
   * Enhanced with theme integration cleanup
   */
  destroy(): void {
    // Remove event listeners
    Object.entries(this.shortcutHandlers).forEach(([_command, handler]) => {
      document.removeEventListener('keydown', handler);
    });

    // Remove command listener
    if (this.commandListener) {
      document.removeEventListener('feather:command', this.commandListener);
    }

    // Clean up theme integration
    this.cleanupThemeIntegration();

    this.onDestroy();
  }

  /**
   * Clean up theme integration resources
   * @protected
   */
  protected cleanupThemeIntegration(): void {
    // Unsubscribe from theme changes
    if (this.themeChangeUnsubscribe) {
      this.themeChangeUnsubscribe();
      this.themeChangeUnsubscribe = undefined;
    }

    // Clean up theme-aware elements
    if (this.elementFactory && this.themeAwareElements.length > 0) {
      this.themeAwareElements.forEach(element => {
        try {
          this.elementFactory!.removeThemeFromElement(element);
        } catch (error) {
          console.warn(`Failed to remove theme from element:`, error);
        }
      });
    }

    // Clear references
    this.themeAwareElements = [];
    this.themeManager = undefined;
    this.elementFactory = undefined;
  }

  /**
   * Called when the plugin is destroyed
   * Override this method to add custom cleanup logic
   * @protected
   */
  protected onDestroy(): void {
    // Override in subclasses
  }
}
