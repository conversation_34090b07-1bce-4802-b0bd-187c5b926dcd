/**
 * Presence Plugin for Feather JS
 * ──────────────────────────────
 * Renders a compact avatar bar that shows **who is currently viewing /
 * editing** the document and fades users out when they go idle.
 *
 * * No dependencies – only Web APIs
 * * Automatic `BroadcastChannel` fallback when no global bus is provided
 * * Heart-beat mechanism so presence survives tab reloads / network hiccups
 */

import { makeBus } from '../../utils/helpers';
import type { FeatherEventBus } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
declare global {
  interface Window { featherEventBus?: FeatherEventBus }
}

/**
 * Plugin configuration
 */
const META: PluginConfig = {
  id:          'presence',
  name:        'Presence',
  description: 'Avatar list of active users',
  version:     '1.0.0',
  toolbarItems: []                                  // visual only, no button
};

export const pluginId = META.id;

/**
 * User object
 */
interface PresenceUser {
  id: string;
  name: string;
  avatar?: string;
  color?: string;
  /** unix ms – last heartbeat we heard from this user */
  lastSeen: number;
}

/**
 * The plugin
 */
class PresencePlugin extends BasePlugin {
  /**
   * The bus
   */
  private readonly bus: FeatherEventBus;

  /**
   * The users
   */
  private readonly users = new Map<string, PresenceUser>();
  private readonly localUserId = `u-${Math.random().toString(36).slice(2, 8)}`;
  private readonly heartbeatInterval = 4_000;     // ms
  private readonly timeoutIdle       = 15_000;    // ms

  private avatarBar?: HTMLElement;

  private abort = new AbortController();          // cleans all listeners / timers

  /**
   * Construction & init
   */
  constructor() {
    super(META);
    /* Share the same bus with other plugins or create a local one */
    this.bus = window.featherEventBus ?? (window.featherEventBus = makeBus('feather-presence'));
  }

  protected override onInit(): void {
    if (!this.editor) return;

    this.initializeBar();
    this.registerBusHandlers();
    this.startHeartbeat();
  }

  public override handleCommand(_cmd: string): void {}

  /**
   * Register bus handlers
   */
  private registerBusHandlers(): void {
    /* Someone else sent a heartbeat */
    this.bus.subscribe(
      'presence:heartbeat',
      (payload) => this.onHeartbeat(payload as PresenceUser)
    );
  }

  private onHeartbeat(incoming: PresenceUser): void {
    /* Never create duplicate local record */
    if (incoming.id === this.localUserId) return;

    const existing = this.users.get(incoming.id);
    if (existing) {
      existing.lastSeen = Date.now();
      /* Allow name / avatar / color updates */
      existing.name   = incoming.name;
      existing.avatar = incoming.avatar;
      existing.color  = incoming.color;
    } else {
      /* New user */
      this.users.set(incoming.id, { ...incoming, lastSeen: Date.now() });
    }
    this.renderBar();
  }

  /**
   * Start the heartbeat
   */
  private startHeartbeat(): void {
    /* Build our static user object once */
    const me: PresenceUser = {
      id: this.localUserId,
      name: 'Me',
      avatar: undefined,
      color: undefined,
      lastSeen: Date.now()
    };

    /* Send immediately so others see us asap */
    this.bus.publish('presence:heartbeat', me);

    /* Keep sending every X ms */
    const send = () => {
      me.lastSeen = Date.now();
      this.bus.publish('presence:heartbeat', me);
    };
    send();

    const timer = window.setInterval(send, this.heartbeatInterval);
    this.abort.signal.addEventListener('abort', () => clearInterval(timer));

    /* Periodic cleanup of idle users */
    const cleaner = window.setInterval(() => this.pruneIdleUsers(), this.heartbeatInterval);
    this.abort.signal.addEventListener('abort', () => clearInterval(cleaner));
  }

  private pruneIdleUsers(): void {
    const now = Date.now();
    let changed = false;
    this.users.forEach((u, key) => {
      if (now - u.lastSeen > this.timeoutIdle) {
        this.users.delete(key);
        changed = true;
      }
    });
    if (changed) this.renderBar();
  }

  /**
   * Render the presence bar
   */
  private renderBar(): void {
    if (!this.avatarBar) return;

    /* Always keep local user on the left */
    const sorted = [
      { id: this.localUserId, name: 'Me', lastSeen: Date.now() },
      ...Array.from(this.users.values()).sort((a, b) => a.name.localeCompare(b.name))
    ];

    this.avatarBar.innerHTML = '';
    sorted.forEach((user) => {
      const chip = document.createElement('span');
      chip.className = 'w-[26px] h-[26px] rounded-full flex-none flex items-center justify-center text-[13px] font-semibold overflow-hidden border border-black/10 dark:border-white/10 cursor-default';
      chip.title = user.name;

      if (user.avatar) {
        const img = document.createElement('img');
        img.src = user.avatar;
        img.alt = user.name;
        img.className = 'w-full h-full object-cover rounded-full';
        chip.appendChild(img);
      } else {
        chip.textContent = user.name[0]?.toUpperCase() ?? '?';
        chip.classList.add('text-white'); // Default text color for initials
        if (user.color) {
          chip.style.backgroundColor = user.color;
        } else {
          // Apply a default background if no user color is specified
          chip.classList.add('bg-slate-500', 'dark:bg-slate-400');
        }
      }
      this.avatarBar!.appendChild(chip);
    });
  }

  private initializeBar(): void {
    if (!this.avatarBar) {
      this.avatarBar = document.createElement('div');
      this.avatarBar.className = 'feather-presence-bar flex items-center gap-1.5 px-2 py-1 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800 min-h-[34px] select-none font-sans';

      // Apply theme classes to the avatar bar
      const htmlElement = document.documentElement;
      const storedTheme = localStorage.getItem('editor-theme');
      if (htmlElement.classList.contains('dark') || storedTheme === 'dark') {
        this.avatarBar.classList.add('dark');
      }
      if (storedTheme) {
        this.avatarBar.classList.add(`theme-${storedTheme}`);
      }

      /* insert at top of editor container */
      this.editor!.getElement().parentElement!.prepend(this.avatarBar);

      // Listen for theme changes
      document.addEventListener('themechange', (e) => {
        const theme = (e as CustomEvent).detail;
        // Remove existing theme classes
        this.avatarBar?.classList.remove('theme-light', 'theme-dark', 'dark');
        // Add new theme class
        this.avatarBar?.classList.add(`theme-${theme}`);
        if (theme === 'dark') {
          this.avatarBar?.classList.add('dark');
        }
      }, { signal: this.abort.signal });
    }
    this.renderBar(); // call the overload above
  }

  /**
   * Cleanup
   */
  public override destroy(): void {
    this.abort.abort();
    this.avatarBar?.remove();
    super.destroy();
  }
}

const presencePlugin = new PresencePlugin();

export default presencePlugin;
