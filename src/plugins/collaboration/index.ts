import type { Plugin } from '../../types';
// Import all collaboration plugins
import liveCursorsPlugin from './live-cursors';
import commentsPlugin from './comments';
import trackChangesPlugin from './track-changes';
import presencePlugin from './presence';

// Export individual plugins for direct imports
export {
  liveCursorsPlugin,
  commentsPlugin,
  trackChangesPlugin,
  presencePlugin
}

// Export a combined plugin group for convenience
export const CollaborationPlugins: Plugin[] = [
  liveCursorsPlugin,
  commentsPlugin,
  trackChangesPlugin,
  presencePlugin
];

// Export the plugins array directly
// PluginManager will handle individual plugin registration
export default CollaborationPlugins;