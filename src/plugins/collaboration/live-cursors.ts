/**
 * Live-Cursors Plugin for FeatherJS
 * Shows other users' cursor positions with coloured carets.
 */

import type { FeatherEventBus } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
import { makeBus, rafThrottle } from '../../utils/helpers';

declare global {
  interface Window { featherEventBus?: FeatherEventBus; }
}

/* -------------------------------------------------------------------------- */
/*                                  Types                                     */
/* -------------------------------------------------------------------------- */

export interface RemoteUser {
  id: string;
  name: string;
  color: string;
  avatar?: string;
}

/** DOM-path + offset : robust across most edits */
export interface CursorPosition {
  userId: string;
  startPath: number[];
  startOffset: number;
  endPath: number[];
  endOffset: number;
  timestamp: number;
}

export interface LiveCursorsConfig {
  showLabels?: boolean;
  highlightSelections?: boolean;
  defaultColors?: string[];
  broadcastInterval?: number;            // ms
  inactiveTimeout?: number;              // ms
}

/* -------------------------------------------------------------------------- */
/*                         Plugin meta & default config                       */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id: 'live-cursors',
  name: 'Live Cursors',
  description: 'Show other users’ carets',
  version: '2.0.0',
  toolbarItems: [
    {
      id: 'live-cursors',
      command: 'live-cursors',
      icon: '🖱️',
      label: 'Live Cursors',
      tooltip: 'Toggle live-cursors',
      group: 'collaboration',
      ariaLabel: 'Toggle live-cursors'
    }
  ]
};

const DEF = {
  showLabels: true,
  highlightSelections: true,
  defaultColors: ['#f5425c', '#2d9cdb', '#27ae60', '#9b51e0', '#f2994a'],
  broadcastInterval: 80,
  inactiveTimeout: 10_000
};

/* -------------------------------------------------------------------------- */
/*                               Main class                                   */
/* -------------------------------------------------------------------------- */

class LiveCursorsPlugin extends BasePlugin {
  /* merged cfg */
  private cfg: ReturnType<typeof Object.assign>;

  private bus: FeatherEventBus;

  private localId = `u-${Math.random().toString(36).slice(2, 8)}`;
  private users = new Map<string, RemoteUser>();

  /* userId → visual wrapper */
  private wrappers = new Map<string, HTMLElement>();

  /* userId → last position */
  private remotes = new Map<string, CursorPosition>();

  private overlay?: HTMLElement;

  private abort = new AbortController();

  /* ---------------------------- constructor --------------------------- */

  constructor(overrides: LiveCursorsConfig = {}) {
    super(META);
    this.cfg = Object.assign({}, DEF, overrides);

    this.bus = window.featherEventBus || makeBus('feather-live-cursors');
  }

  /* ------------------------ BasePlugin callbacks ---------------------- */

  protected override onInit(): void {
    this.ensureOverlay();
    this.setupListeners();
    this.users.set(this.localId, { id: this.localId, name: 'Me', color: '#000' });

    /* bus handling */
    this.bus.subscribe('cursor:update', (d) =>
      this.onRemote(d as CursorPosition)
    );
  }

  public override handleCommand(cmd: string): void {
    if (cmd === 'live-cursors') this.toggle();
  }

  /* --------------------------- Visibility ----------------------------- */

  private toggle(): void {
    if (!this.editor || !this.overlay) return; // Check for overlay as well
    this.overlay.classList.toggle('hidden');
    const active = !this.overlay.classList.contains('hidden');
    const btn = document.querySelector('[data-command="liveCursors"]') as HTMLElement;
    btn?.classList.toggle('active', active);
  }

  /* --------------------------- Local cursor --------------------------- */

  private setupListeners(): void {
    if (!this.editor) return;

    const send = rafThrottle(() => this.sendLocalCursor());

    const events: (keyof GlobalEventHandlersEventMap)[] = [
      'mouseup',
      'keyup',
      'click',
      'focus',
      'touchend',
      'selectionchange'
    ];

    events.forEach((ev) =>
      document.addEventListener(ev, send, { signal: this.abort.signal })
    );

    /* cleanup inactive remotes */
    setInterval(() => this.prune(), this.cfg.inactiveTimeout / 2);
  }

  private sendLocalCursor(): void {
    if (!this.editor) return;

    const sel = window.getSelection();
    if (!sel?.rangeCount) return;

    const range = sel.getRangeAt(0);
    if (!this.editor!.getElement().contains(range.commonAncestorContainer)) return;

    const pos = this.rangeToPos(range);
    if (!pos) return;

    const payload: CursorPosition = {
      userId: this.localId,
      ...pos,
      timestamp: Date.now()
    };
    this.bus.publish('cursor:update', payload);
  }

  /* --------------------------- Remote cursor -------------------------- */

  private onRemote(pos: CursorPosition): void {
    if (pos.userId === this.localId) return;

    /* store */
    this.remotes.set(pos.userId, pos);

    /* user color */
    if (!this.users.has(pos.userId)) {
      const idx = this.users.size % this.cfg.defaultColors.length;
      this.users.set(pos.userId, {
        id: pos.userId,
        name: `User ${this.users.size}`,
        color: this.cfg.defaultColors[idx]
      });
    }
    this.render(pos);
  }

  private prune(): void {
    const now = Date.now();
    this.remotes.forEach((p, id) => {
      if (now - p.timestamp > this.cfg.inactiveTimeout) {
        this.wrappers.get(id)?.remove();
        this.wrappers.delete(id);
        this.remotes.delete(id);
      }
    });
  }

  /* --------------------------- Rendering ------------------------------ */

  private ensureOverlay(): void {
    if (this.overlay) return;
    const ed = this.editor!.getElement();
    this.overlay = document.createElement('div');
    this.overlay.className = 'absolute inset-0 w-full h-full pointer-events-none z-10 overflow-hidden';
    ed.appendChild(this.overlay);
    // Ensure the editor element itself is a positioning context if not already.
    if (window.getComputedStyle(ed).position === 'static') {
      ed.style.position = 'relative';
    }
  }

  private render(pos: CursorPosition): void {
    if (!this.overlay) return;
    const range = this.posToRange(pos);
    if (!range) return;

    const user = this.users.get(pos.userId)!;
    let wrap = this.wrappers.get(pos.userId);
    if (!wrap) {
      wrap = document.createElement('div');
      wrap.className = 'absolute'; // Tailwind for wrapper
      wrap.style.setProperty('--feather-cursor-color', user.color);

      const caretDiv = document.createElement('div');
      caretDiv.className = 'absolute top-0 -left-px w-0.5 h-full bg-[var(--feather-cursor-color)] opacity-100 animate-cursor-blink';
      wrap.appendChild(caretDiv);

      if (this.cfg.showLabels) {
        const labelDiv = document.createElement('div');
        labelDiv.className = 'absolute top-[-1.4em] -left-0.5 px-1 py-0 text-xs text-white bg-[var(--feather-cursor-color)] rounded-sm whitespace-nowrap select-none';
        labelDiv.textContent = user.name;
        wrap.appendChild(labelDiv);
      }

      this.overlay.appendChild(wrap);
      this.wrappers.set(pos.userId, wrap);
    }

    /* caret position */
    const rect = range.getBoundingClientRect();
    const eRect = this.editor!.getElement().getBoundingClientRect();

    wrap.style.transform = `translate(${rect.left - eRect.left + this.editor!.getElement().scrollLeft}px,
                                      ${rect.top - eRect.top + this.editor!.getElement().scrollTop}px)`;
    wrap.style.height = `${rect.height}px`;

    /* selection rectangles */
    if (this.cfg.highlightSelections) {
      wrap.querySelectorAll('.feather-live-cursor-sel').forEach((n) => n.remove());
      if (!range.collapsed) {
        for (const r of Array.from(range.getClientRects())) {
          const s = document.createElement('div');
          s.className = 'absolute opacity-30 mix-blend-multiply bg-[var(--feather-cursor-color)]'; // Tailwind for selection
          Object.assign(s.style, {
            left: `${r.left - eRect.left + this.editor!.getElement().scrollLeft}px`,
            top: `${r.top - eRect.top + this.editor!.getElement().scrollTop}px`,
            width: `${r.width}px`,
            height: `${r.height}px`,
          });
          wrap.appendChild(s);
        }
      }
    }
  }

  /* -------------------- Range ↔︎ path/offset helpers ------------------- */

  private nodeToPath(node: Node): number[] | null {
    if (!this.editor) return null;
    const path: number[] = [];
    let cur: Node | null = node;
    while (cur && cur !== this.editor!.getElement()) {
      const parent = cur.parentNode as Node;
      if (!parent) return null;
      path.unshift(Array.prototype.indexOf.call(parent.childNodes, cur));
      cur = parent;
    }
    return path;
  }

  private pathToNode(path: number[]): Node | null {
    if (!this.editor) return null;
    let cur: Node = this.editor!.getElement();
    for (const idx of path) {
      cur = cur.childNodes[idx] as Node;
      if (!cur) return null;
    }
    return cur;
  }

  private rangeToPos(r: Range): Omit<CursorPosition, 'userId' | 'timestamp'> | null {
    const sp = this.nodeToPath(r.startContainer);
    const ep = this.nodeToPath(r.endContainer);
    if (!sp || !ep) return null;
    return {
      startPath: sp,
      startOffset: r.startOffset,
      endPath: ep,
      endOffset: r.endOffset
    };
  }

  private posToRange(p: CursorPosition): Range | null {
    const s = this.pathToNode(p.startPath);
    const e = this.pathToNode(p.endPath);
    if (!s || !e) return null;
    const r = document.createRange();
    r.setStart(s, p.startOffset);
    r.setEnd(e, p.endOffset);
    return r;
  }

  /* ----------------------------- Destroy ------------------------------- */

  public override destroy(): void {
    this.abort.abort();
    this.overlay?.remove();
    this.wrappers.forEach((w) => w.remove());
    this.wrappers.clear();
    super.destroy();
  }
}

// Create and export the plugin instance directly
const plugin = new LiveCursorsPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = META.id;

// Default export for tree-shaking
export default plugin;
