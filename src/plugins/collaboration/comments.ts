/**
 * Comments Plugin for FeatherJS
 * Provides collaborative comments functionality with a side panel.
 */

// src/plugins/collaboration/comments.ts
import type { FeatherEventBus } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
import { makeBus, rafThrottle } from '../../utils/helpers';
import { PluginError, DOM<PERSON>rror, NetworkError, ErrorHandler } from '../../utils/error';
import { Logger, LogLevel } from '../../utils/logger';

declare global {
  interface Window {
    featherEventBus?: FeatherEventBus;
  }
}

export interface CommentUser {
  id: string;
  name: string;
  avatar?: string;
  color?: string;
}

export interface CommentPosition {
  startPath: number[];
  startOffset: number;
  endPath: number[];
  endOffset: number;
  text?: string;
}

export interface Comment {
  id: string;
  userId: string;
  content: string;
  position: CommentPosition;
  createdAt: Date;
  updatedAt?: Date;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  parentId?: string | null;
}

/* -------------------------------------------------------------------------- */
/*                              Plugin metadata                               */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id: 'comments',
  name: 'Comments',
  description: 'Collaborative comments',
  version: '2.0.0',
  toolbarItems: [
    {
      id: 'comments',
      command: 'comments',
      icon: '💬',
      label: 'Comments',
      tooltip: 'Toggle comments panel',
      group: 'collaboration',
      ariaLabel: 'Toggle comments panel'
    }
  ],
  shortcuts: [
    {
      key: 'm',
      command: 'comments',
      altKey: true,
      ctrlKey: true,
      preventDefault: true
    }
  ]
};

const DEFAULTS = {
  panelWidth: 300,
  highlightColor: 'rgba(255,230,0,.35)', // This will be used as a CSS variable value
  highlightComments: true,
  showResolved: true,
  allowDelete: true,
  allowEdit: true,
  commentPlaceholder: 'Add a comment…',
  panelAriaLabel: 'Comments panel',
  openByDefault: false
};

/* -------------------------------------------------------------------------- */
/*                                 The plugin                                 */
/* -------------------------------------------------------------------------- */

class CommentsPlugin extends BasePlugin {
  /** configuration after merging defaults+user */
  private cfg: ReturnType<typeof Object.assign>;
  private bus: FeatherEventBus;

  private users = new Map<string, CommentUser>();
  private comments = new Map<string, Comment>();

  /* UID → array of overlay <div>s */
  private highlights = new Map<string, HTMLElement[]>();

  private overlayLayer?: HTMLElement;

  private panel?: HTMLElement;
  private isOpen = false;

  private localId = `u-${Math.random().toString(36).slice(2, 8)}`;

  private seed = 0;
  private selectedRange?: Range;

  /** one abort controller kills *all* listeners & timers */
  private abort = new AbortController();

  /** Logger instance for this plugin */
  private logger: Logger;

  constructor(userCfg = {}) {
    super(META);
    this.cfg = Object.assign({}, DEFAULTS, userCfg);

    this.logger = new Logger({
      level: LogLevel.INFO,
      source: 'CommentsPlugin',
      metadata: { pluginId: META.id, version: META.version }
    });

    try {
      this.bus = window.featherEventBus || makeBus('feather-comments');
      this.logger.debug('Event bus initialized');
    } catch (error) {
      const networkError = new NetworkError('Failed to initialize event bus', undefined, undefined, error);
      this.logger.error('Event bus initialization failed', networkError);
      this.bus = makeBus('feather-comments-fallback'); // Fallback
    }
  }

  protected override onInit(): void {
    try {
      this.logger.info('Initializing Comments plugin');
      this.ensureOverlay();
      this.setupUser();
      this.setupToolbar();
      // this.registerShortcuts(); // Assuming BasePlugin handles this

      if (this.cfg.openByDefault) {
        this.togglePanel();
      }

      this.bus.subscribe('comment:create', (d) => {
        ErrorHandler.try(() => this.onRemoteCreate(d as Comment),
          error => this.logger.error('Error handling remote comment creation', error));
      });

      this.bus.subscribe('comment:update', (d) => {
        ErrorHandler.try(() => this.onRemoteUpdate(d as Comment),
          error => this.logger.error('Error handling remote comment update', error));
      });

      this.bus.subscribe('comment:delete', (d) => {
        ErrorHandler.try(() => this.onRemoteDelete(d as { id: string }),
          error => this.logger.error('Error handling remote comment deletion', error));
      });

      this.logger.info('Comments plugin initialized successfully');
    } catch (error) {
      const pluginError = new PluginError('Failed to initialize Comments plugin', META.id, error);
      this.logger.error('Initialization failed', pluginError);
    }
  }

  public override handleCommand(cmd: string): void {
    try {
      this.logger.debug(`Handling command: ${cmd}`);
      if (cmd === 'comments') this.togglePanel();
    } catch (error) {
      const pluginError = new PluginError(`Error handling command: ${cmd}`, META.id, { command: cmd, error });
      this.logger.error('Command execution failed', pluginError);
    }
  }

  private togglePanel(): void {
    try {
      if (this.isOpen) {
        this.closePanel();
      } else {
        this.openPanel();
      }
    } catch (err) {
      this.logger.error('Error toggling panel', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private openPanel(): void {
    try {
      if (this.isOpen) return;

      if (!this.panel) {
        this.panel = this.buildPanel();
        if (!this.panel) {
          this.logger.error('Failed to build panel');
          return;
        }
        document.body.appendChild(this.panel);
      }

      this.panel.classList.remove('translate-x-full');
      this.panel.classList.add('translate-x-0');
      this.isOpen = true;

      const btn = document.querySelector('[data-command="comments"]') as HTMLElement;
      if (btn) {
        btn.setAttribute('aria-pressed', 'true');
      }
      this.renderPanel();
    } catch (err) {
      this.logger.error('Error opening panel', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private closePanel(): void {
    try {
      if (!this.isOpen || !this.panel) return;

      this.panel.classList.remove('translate-x-0');
      this.panel.classList.add('translate-x-full');
      this.isOpen = false;

      const btn = document.querySelector('[data-command="comments"]') as HTMLElement;
      if (btn) {
        btn.setAttribute('aria-pressed', 'false');
      }
    } catch (err) {
      this.logger.error('Error closing panel', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private buildPanel(): HTMLElement {
    try {
      const panel = document.createElement('div');
      panel.className = `feather-comment-panel fixed top-0 bottom-0 right-0 w-[${this.cfg.panelWidth}px] bg-gray-50 dark:bg-slate-800 border-l border-gray-300 dark:border-slate-700 shadow-lg flex flex-col z-20 overflow-hidden transition-transform duration-300 ease-in-out translate-x-full`;
      panel.setAttribute('role', 'complementary');
      panel.setAttribute('aria-label', this.cfg.panelAriaLabel || 'Comments panel');

      // Apply theme classes to the panel
      const htmlElement = document.documentElement;
      const storedTheme = localStorage.getItem('editor-theme');
      if (htmlElement.classList.contains('dark') || storedTheme === 'dark') {
        panel.classList.add('dark');
      }
      if (storedTheme) {
        panel.classList.add(`theme-${storedTheme}`);
      }

      // Listen for theme changes
      document.addEventListener('themechange', (e) => {
        const theme = (e as CustomEvent).detail;
        // Remove existing theme classes
        panel.classList.remove('theme-light', 'theme-dark', 'dark');
        // Add new theme class
        panel.classList.add(`theme-${theme}`);
        if (theme === 'dark') {
          panel.classList.add('dark');
        }
      }, { signal: this.abort.signal });

      const header = document.createElement('header');
      header.className = 'px-4 py-3 border-b border-gray-200 dark:border-slate-700 flex justify-between items-center flex-shrink-0 bg-gray-100 dark:bg-slate-700';

      const title = document.createElement('h3');
      title.className = 'm-0 text-base font-semibold text-gray-900 dark:text-slate-100';
      title.textContent = 'Comments';

      const actionsDiv = document.createElement('div');
      actionsDiv.className = 'flex items-center gap-2';

      const addCommentBtn = document.createElement('button');
      addCommentBtn.className = 'py-1 px-2 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded disabled:opacity-50';
      addCommentBtn.textContent = '+ Add';
      addCommentBtn.setAttribute('aria-label', 'Add new comment');
      addCommentBtn.disabled = true;
      addCommentBtn.addEventListener('click', () => {
        this.closePanel();
        setTimeout(() => this.onAddComment(), 100);
      });
      actionsDiv.appendChild(addCommentBtn);

      const closeBtn = document.createElement('button');
      closeBtn.className = 'bg-transparent border-none text-xl cursor-pointer p-1 leading-none text-gray-600 dark:text-slate-300 opacity-70 hover:opacity-100';
      closeBtn.textContent = '×';
      closeBtn.setAttribute('aria-label', 'Close comments panel');
      closeBtn.addEventListener('click', () => this.closePanel());
      actionsDiv.appendChild(closeBtn);

      header.appendChild(title);
      header.appendChild(actionsDiv);

      const body = document.createElement('div');
      body.className = 'flex-grow overflow-y-auto';

      const commentList = document.createElement('div');
      commentList.className = 'fc-comment-list p-2'; // Keep specific class for querying
      body.appendChild(commentList);

      const footer = document.createElement('footer');
      footer.className = 'p-2 border-t border-gray-200 dark:border-slate-700 text-xs text-gray-500 dark:text-slate-400 text-center flex-shrink-0';
      footer.textContent = 'FeatherJS Comments';

      panel.appendChild(header);
      panel.appendChild(body);
      panel.appendChild(footer);

      const updateAddButtonState = rafThrottle(() => {
        try {
          const selection = window.getSelection();
          const hasValidSelection = selection && !selection.isCollapsed && selection.rangeCount > 0;
          addCommentBtn.disabled = !hasValidSelection;
        } catch (err) {
          this.logger.error('Error updating button state', err instanceof Error ? err : new Error(String(err)));
        }
      });

      document.addEventListener('selectionchange', updateAddButtonState, { signal: this.abort.signal });
      updateAddButtonState();

      return panel;
    } catch (err) {
      this.logger.error('Error building panel', err instanceof Error ? err : new Error(String(err)));
      const errorPanel = document.createElement('div');
      errorPanel.className = 'fc-panel fc-error fixed top-0 right-0 w-64 h-full bg-red-100 p-4 text-red-700';
      errorPanel.textContent = 'Error building comments panel';
      return errorPanel;
    }
  }

  private renderPanel(): void {
    try {
      if (!this.panel) {
        this.logger.warn('Panel not created yet, cannot render.');
        return;
      }
      const container = this.panel.querySelector<HTMLElement>('.fc-comment-list');
      if (!container) {
        this.logger.warn('Comment list container not found in panel.');
        return;
      }
      container.innerHTML = '';
      if (this.comments.size === 0) {
        const emptyState = document.createElement('div');
        emptyState.className = 'text-center text-gray-500 dark:text-slate-400 italic p-4';
        emptyState.textContent = 'No comments yet';
        container.appendChild(emptyState);
        return;
      }
      this.renderTree(null, container);
    } catch (err) {
      this.logger.error('Error rendering panel content', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private renderTree(pid: string | null, container: HTMLElement): void {
    const byParent = new Map<string | null, Comment[]>();
    this.comments.forEach((c) => {
      const key = c.parentId === undefined ? null : c.parentId;
      const items = byParent.get(key) || [];
      if (!byParent.has(key)) {
        byParent.set(key, items);
      }
      items.push(c);
    });

    const childrenOfPid = byParent.get(pid) ?? [];
    childrenOfPid.sort((a,b) => a.createdAt.getTime() - b.createdAt.getTime());

    for (const c of childrenOfPid) {
      const el = this.commentItem(c);
      container.appendChild(el);
      if (byParent.has(c.id) && (byParent.get(c.id) || []).length > 0) {
        const nestedContainer = document.createElement('div');
        // Tailwind for replies container, e.g., 'ml-4 pl-4 border-l border-gray-200 dark:border-gray-700'
        // For now, just append to the item, specific styling for nesting can be added if needed.
        el.appendChild(nestedContainer);
        this.renderTree(c.id, nestedContainer);
      }
    }
  }

  private commentItem(c: Comment): HTMLElement {
    const itemDiv = document.createElement('div');
    let itemClasses = 'px-4 py-3 border-b border-gray-200 dark:border-slate-700 relative';
    if (c.resolved) {
      itemClasses += ' opacity-60 bg-gray-100 dark:bg-slate-700/50';
    }
    if (c.parentId) {
      itemClasses += ' ml-6 pl-2 border-l-2 border-gray-300 dark:border-slate-600';
    }
    itemDiv.className = itemClasses;
    itemDiv.dataset.id = c.id;

    const user = this.users.get(c.userId);

    const headerEl = document.createElement('header');
    headerEl.className = 'flex items-center mb-1';

    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'w-6 h-6 rounded-full mr-2 flex items-center justify-center font-bold text-white text-xs overflow-hidden';
    if (user?.color) {
      avatarDiv.style.backgroundColor = user.color;
    } else {
      avatarDiv.classList.add('bg-gray-400', 'dark:bg-slate-500');
    }

    if (user?.avatar) {
      const img = document.createElement('img');
      img.src = user.avatar;
      img.alt = user.name ?? 'User';
      img.className = 'w-full h-full object-cover';
      avatarDiv.appendChild(img);
    } else {
      avatarDiv.textContent = (user?.name ?? 'U').substring(0, 1).toUpperCase();
    }
    headerEl.appendChild(avatarDiv);

    const metaDiv = document.createElement('div');
    metaDiv.className = 'text-xs text-gray-500 dark:text-slate-400';
    const userNameSpan = document.createElement('span');
    userNameSpan.className = 'font-semibold text-gray-800 dark:text-slate-100 mr-1.5';
    userNameSpan.textContent = user?.name ?? 'User';
    const timestampSpan = document.createElement('span');
    timestampSpan.className = 'opacity-80';
    timestampSpan.title = c.createdAt.toISOString();
    timestampSpan.textContent = c.createdAt.toLocaleString();
    metaDiv.appendChild(userNameSpan);
    metaDiv.appendChild(timestampSpan);
    headerEl.appendChild(metaDiv);
    itemDiv.appendChild(headerEl);

    const contentDiv = document.createElement('div');
    contentDiv.className = 'my-2 text-sm leading-normal whitespace-pre-wrap break-words text-gray-800 dark:text-slate-200';
    contentDiv.textContent = c.content;
    itemDiv.appendChild(contentDiv);

    const footerEl = document.createElement('footer');
    footerEl.className = 'flex gap-2 mt-2';
    itemDiv.appendChild(footerEl);

    const addBtn = (label: string, fn: () => void) => {
      const b = document.createElement('button');
      b.className = 'bg-transparent border-none text-blue-600 dark:text-blue-400 cursor-pointer text-xs py-0.5 opacity-80 hover:opacity-100 hover:underline';
      b.textContent = label; b.type = 'button';
      b.addEventListener('click', fn, { signal: this.abort.signal });
      footerEl.appendChild(b);
    };

    addBtn('Reply', () =>
      this.openDialog('Reply…', (txt) => this.addComment(txt, c.position, c.id))
    );

    addBtn(c.resolved ? 'Re-open' : 'Resolve', () => {
      c.resolved = !c.resolved;
      if (c.resolved) {
        c.resolvedAt = new Date();
        c.resolvedBy = this.localId;
      } else {
        c.resolvedAt = undefined;
        c.resolvedBy = undefined;
      }
      this.renderPanel();
      this.bus.publish('comment:update', c);
    });

    if (c.userId === this.localId && this.cfg.allowEdit) {
      addBtn('Edit', () =>
        this.openDialog('Edit comment…', (txt) => {
          c.content = txt;
          c.updatedAt = new Date();
          this.renderPanel();
          this.bus.publish('comment:update', c);
        }, c.content)
      );
    }

    if (c.userId === this.localId && this.cfg.allowDelete) {
      addBtn('Delete', () => {
        if (confirm('Delete comment?')) {
          this.comments.delete(c.id);
          this.removeHighlight(c.id);
          this.renderPanel();
          this.bus.publish('comment:delete', { id: c.id });
        }
      });
    }
    return itemDiv;
  }

  private onAddComment(): void {
    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
        alert('Please select some text to comment on');
        return;
      }
      const range = selection.getRangeAt(0);
      this.selectedRange = range.cloneRange();

      const position = this.rangeToPos(range);
      if (!position) {
        alert('Cannot comment on this selection');
        return;
      }
      this.openDialog(this.cfg.commentPlaceholder, (text) => this.addComment(text, position));
    } catch (err) {
      this.logger.error('Error adding comment', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private addComment(txt: string, pos: CommentPosition, parentId: string | null = null): void {
    try {
    const id = `c-${Date.now()}-${++this.seed}`;
    const comment: Comment = {
      id,
      userId: this.localId,
      content: txt,
      position: pos,
      createdAt: new Date(),
      resolved: false,
      parentId
    };
    this.comments.set(id, comment);
    if (this.cfg.highlightComments && this.selectedRange) {
      this.addHighlight(id, this.selectedRange);
    }
    this.renderPanel();
    this.bus.publish('comment:create', comment);
    } catch (err) {
      this.logger.error('Error adding comment', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private onRemoteCreate(c: Comment): void {
    try {
      if (this.comments.has(c.id) || c.userId === this.localId) return;
      this.comments.set(c.id, c);

      if (this.cfg.highlightComments) {
        const range = this.posToRange(c.position);
        if (range) this.addHighlight(c.id, range);
      }

      this.renderPanel();
    } catch (err) {
      this.logger.error('Error processing remote comment', err instanceof Error ? err : new Error(String(err)));
    }
  }
  private onRemoteUpdate(c: Comment): void {
    try {
      const old = this.comments.get(c.id);
      if (!old) return;
      this.comments.set(c.id, c);
      if (this.isOpen) this.renderPanel();
    } catch (err) {
      this.logger.error('Error updating remote comment', err instanceof Error ? err : new Error(String(err)));
    }
  }
  private onRemoteDelete({ id }: { id: string }): void {
    try {
      if (!this.comments.has(id)) return;
      this.comments.delete(id);
      this.removeHighlight(id);
      if (this.isOpen) this.renderPanel();
    } catch (err) {
      this.logger.error('Error deleting remote comment', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private nodeToPath(node: Node): number[] | null {
    try {
      if (!this.editor) return null;
      const path: number[] = [];
      let cur: Node | null = node;
      const editorElement = this.editor.getElement();
      if (!editorElement) return null;

      while (cur && cur !== editorElement) {
        const parent = cur.parentNode as Node;
        if (!parent) return null;
        path.unshift(Array.prototype.indexOf.call(parent.childNodes, cur));
        cur = parent;
      }
      return path;
    } catch (err) {
      this.logger.error('Error getting node path', err instanceof Error ? err : new Error(String(err)));
      return null;
    }
  }

  private pathToNode(path: number[]): Node | null {
    try {
      if (!this.editor) return null;
      const editorElement = this.editor.getElement();
      if (!editorElement) return null;

      let node: Node | null = editorElement;
      for (const idx of path) {
        if (!node || !node.childNodes || !node.childNodes[idx]) return null;
        node = node.childNodes[idx];
      }
      return node;
    } catch (err) {
      this.logger.error('Error getting node from path', err instanceof Error ? err : new Error(String(err)));
      return null;
    }
  }

  private rangeToPos(r: Range): CommentPosition | null {
    try {
      const startPath = this.nodeToPath(r.startContainer);
      const endPath = this.nodeToPath(r.endContainer);
      if (!startPath || !endPath) return null;
      return {
        startPath, startOffset: r.startOffset,
        endPath, endOffset: r.endOffset,
        text: r.toString()
      };
    } catch (err) {
      this.logger.error('Error converting range to position', err instanceof Error ? err : new Error(String(err)));
      return null;
    }
  }

  private posToRange(pos: CommentPosition): Range | null {
    try {
      const startNode = this.pathToNode(pos.startPath);
      const endNode = this.pathToNode(pos.endPath);
      if (!startNode || !endNode) return null;

      const range = document.createRange();
      range.setStart(startNode, pos.startOffset);
      range.setEnd(endNode, pos.endOffset);
      return range;
    } catch (err) {
      this.logger.error('Error converting position to range', err instanceof Error ? err : new Error(String(err)));
      return null;
    }
  }

  private ensureOverlay(): void {
    if (this.overlayLayer) return;

    const edElement = this.editor?.getElement();
    if (!edElement) {
      this.logger.warn('Editor element not found in ensureOverlay');
      return;
    }

    const edContainer = edElement.parentElement;
    if (!edContainer) {
      this.logger.warn('Editor container not found in ensureOverlay');
      return;
    }

    const overlay = document.createElement('div');
    overlay.className = 'absolute inset-0 pointer-events-none z-[5]';
    this.overlayLayer = document.createElement('div');
    this.overlayLayer.className = 'absolute inset-0';

    overlay.appendChild(this.overlayLayer);
    edContainer.appendChild(overlay);
  }

  private addHighlight(id: string, range: Range): void {
    if (!this.overlayLayer) this.ensureOverlay();
    if (!this.overlayLayer) return;

    const editorElement = this.editor?.getElement();
    if (!editorElement) {
      this.logger.warn('Editor element not found when adding highlight');
      return;
    }

    this.removeHighlight(id);

    try {
      const edRect = editorElement.getBoundingClientRect();
      const rects = Array.from(range.getClientRects());
      if (rects.length === 0) {
        this.logger.warn('No client rects found for range');
        return;
      }

      const els: HTMLElement[] = [];
      const highlightColor = this.cfg.highlightColor;

      for (const rect of rects) {
        const hl = document.createElement('div');
        hl.className = 'absolute rounded-sm transition-colors duration-200 ease-in-out cursor-pointer hover:brightness-110';
        hl.style.backgroundColor = highlightColor;
        const left = rect.left - edRect.left + (editorElement.scrollLeft || 0);
        const top = rect.top - edRect.top + (editorElement.scrollTop || 0);

        Object.assign(hl.style, {
          left: `${left}px`,
          top: `${top}px`,
          width: `${Math.max(rect.width, 1)}px`,
          height: `${Math.max(rect.height, 1)}px`,
        });

        hl.style.setProperty('--fc-highlight-color', highlightColor);
        hl.dataset.commentId = id;

        hl.addEventListener('click', () => this.scrollToComment(id));

        this.overlayLayer.appendChild(hl);
        els.push(hl);
      }

      this.highlights.set(id, els);
    } catch (err) {
      this.logger.error('Error adding highlight', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private removeHighlight(id: string): void {
    try {
      const elements = this.highlights.get(id);
      if (elements && elements.length > 0) {
        elements.forEach((el) => {
          if (el && el.parentNode) {
            el.remove();
          }
        });
      }
      this.highlights.delete(id);
    } catch (err) {
      this.logger.error('Error removing highlight', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private scrollToComment(id: string): void {
    try {
      if (!this.panel) {
        this.logger.warn('Panel not available for scrolling');
        return;
      }

      const commentElement = this.panel.querySelector(`[data-id="${id}"]`);
      if (!commentElement) {
        this.logger.warn(`Comment element with id ${id} not found`);
        return;
      }

      commentElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    } catch (err) {
      this.logger.error('Error scrolling to comment', err instanceof Error ? err : new Error(String(err)));
    }
  }

  private openDialog(
    placeholder: string,
    onSubmit: (txt: string) => void,
    initial = ''
  ): void {
    try {
      this.logger.debug('Opening comment dialog');

      const backdropElement = document.createElement('div');
      backdropElement.className = 'fixed inset-0 bg-black/50 z-[100]';

      const dialogElement = document.createElement('div');
      dialogElement.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 p-6 rounded-lg shadow-xl z-[101] min-w-[300px] max-w-lg text-gray-900 dark:text-slate-200';
      dialogElement.setAttribute('role', 'dialog');
      dialogElement.setAttribute('aria-modal', 'true');
      dialogElement.setAttribute('aria-labelledby', 'fc-dialog-title');

      const titleDiv = document.createElement('div');
      titleDiv.id = 'fc-dialog-title';
      titleDiv.className = 'text-lg font-semibold mb-4 text-gray-900 dark:text-slate-100';
      titleDiv.textContent = 'Add Comment';
      dialogElement.appendChild(titleDiv);

      const textareaElement = document.createElement('textarea');
      textareaElement.rows = 4;
      textareaElement.placeholder = placeholder;
      textareaElement.value = initial;
      textareaElement.className = 'w-full block mb-4 p-2 border border-gray-300 dark:border-slate-600 rounded font-inherit text-sm resize-y bg-white dark:bg-slate-700 focus:ring-blue-500 focus:border-blue-500';
      dialogElement.appendChild(textareaElement);

      const footerEl = document.createElement('footer');
      footerEl.className = 'flex justify-end gap-2';

      const cancelButton = document.createElement('button');
      cancelButton.type = 'button';
      cancelButton.className = 'py-2 px-4 rounded border border-gray-300 dark:border-slate-500 text-gray-800 dark:text-slate-100 bg-gray-200 dark:bg-slate-600 hover:bg-gray-300 dark:hover:bg-slate-500 font-medium';
      cancelButton.textContent = 'Cancel';
      footerEl.appendChild(cancelButton);

      const okButton = document.createElement('button');
      okButton.type = 'button';
      okButton.className = 'py-2 px-4 rounded border border-blue-600 dark:border-blue-500 text-white bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 font-medium';
      okButton.textContent = 'OK';
      footerEl.appendChild(okButton);
      dialogElement.appendChild(footerEl);

      document.body.append(backdropElement, dialogElement);

      const focusables = dialogElement.querySelectorAll<HTMLElement>('textarea,button');
      const trap = (e: KeyboardEvent) => {
        if (e.key !== 'Tab') return;
        const first = focusables[0];
        const last = focusables[focusables.length - 1];
        if (e.shiftKey && document.activeElement === first) {
          e.preventDefault(); last.focus();
        } else if (!e.shiftKey && document.activeElement === last) {
          e.preventDefault(); first.focus();
        }
      };

      const handleEsc = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          e.preventDefault();
          closeDialogLocal();
        }
      };

      dialogElement.addEventListener('keydown', trap);
      document.addEventListener('keydown', handleEsc);

      const closeDialogLocal = () => {
        this.logger.debug('Closing comment dialog');
        dialogElement.removeEventListener('keydown', trap);
        document.removeEventListener('keydown', handleEsc);
        dialogElement.remove();
        backdropElement.remove();
      };

      cancelButton.addEventListener('click', closeDialogLocal);
      okButton.addEventListener('click', () => {
        const txt = textareaElement.value.trim();
        if (txt) {
          this.logger.debug('Comment submitted', { length: txt.length });
          onSubmit(txt);
        }
        closeDialogLocal();
      });

      textareaElement.focus();
    } catch (error) {
      const domError = new DOMError('Error opening dialog', undefined, undefined, error);
      this.logger.error('Dialog creation failed', domError);
    }
  }

  private setupUser(): void {
    this.users.set(this.localId, { id: this.localId, name: 'Me' });
  }

  private setupToolbar(): void {
    try {
      this.logger.debug('Setting up toolbar');
      const btn = document.querySelector('[data-command="comments"]') as HTMLElement;
      if (btn) {
        btn.setAttribute('aria-pressed', 'false');
        btn.setAttribute('role', 'button');
        btn.setAttribute('tabindex', '0');
        this.logger.debug('Toolbar button configured successfully');
      } else {
        this.logger.warn('Toolbar button not found', { command: 'comments' });
      }
    } catch (error) {
      const domError = new DOMError('Error setting up toolbar', null, 'toolbar-button', { error });
      this.logger.error('Toolbar setup failed', domError);
    }
  }

  public override destroy(): void {
    try {
      this.logger.info('Destroying Comments plugin');
      this.abort.abort();
      this.overlayLayer?.remove();
      this.panel?.remove();
      this.logger.debug('Successfully cleaned up DOM elements');
      super.destroy();
      this.logger.info('Comments plugin destroyed successfully');
    } catch (error) {
      const pluginError = new PluginError('Error during plugin cleanup', META.id, error);
      this.logger.error('Destroy operation failed', pluginError);
      ErrorHandler.try(() => super.destroy());
    }
  }
}

const commentsPlugin = new CommentsPlugin();

export const pluginId = META.id;
export default commentsPlugin;


