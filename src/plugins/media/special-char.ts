import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

/**
 * Special Characters plugin configuration
 */
const config: PluginConfig = {
  id: 'special-char',
  name: 'Special Characters',
  description: 'Insert special characters and symbols',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'special-char',
      command: 'special-char',
      icon: 'Ω',
      label: 'Special Characters',
      tooltip: 'Insert special characters',
      group: 'special',
      ariaLabel: 'Insert special characters',
    }
  ],
  shortcuts: [] // No keyboard shortcuts to avoid conflicts
};

// Special characters by category
interface SymbolItem {
  char: string;
  name: string;
  description?: string;
}

type SymbolCategory = Record<string, SymbolItem[]>;

const symbolData: SymbolCategory = {
  'Math': [
    { char: '±', name: 'Plus-Minus Sign', description: 'Used for indicating tolerance' },
    { char: '×', name: 'Multiplication Sign', description: 'Used for multiplication' },
    { char: '÷', name: 'Division Sign', description: 'Used for division' },
    { char: '≠', name: 'Not Equal To', description: 'Used to indicate inequality' },
    { char: '≈', name: 'Almost Equal To', description: 'Used for approximations' },
    { char: '≤', name: 'Less-Than or Equal To', description: 'Used for comparison' },
    { char: '≥', name: 'Greater-Than or Equal To', description: 'Used for comparison' },
    { char: '∞', name: 'Infinity', description: 'Represents infinity' },
    { char: '∑', name: 'Summation', description: 'Used for summation' },
    { char: '∫', name: 'Integral', description: 'Used for integration' },
    { char: '∏', name: 'Product', description: 'Used for product notation' },
    { char: '√', name: 'Square Root', description: 'Used for square root' },
    { char: '∂', name: 'Partial Differential', description: 'Used in calculus' }
  ],
  'Greek': [
    { char: 'α', name: 'Alpha', description: 'Lowercase Alpha' },
    { char: 'β', name: 'Beta', description: 'Lowercase Beta' },
    { char: 'γ', name: 'Gamma', description: 'Lowercase Gamma' },
    { char: 'δ', name: 'Delta', description: 'Lowercase Delta' },
    { char: 'ε', name: 'Epsilon', description: 'Lowercase Epsilon' },
    { char: 'ζ', name: 'Zeta', description: 'Lowercase Zeta' },
    { char: 'η', name: 'Eta', description: 'Lowercase Eta' },
    { char: 'θ', name: 'Theta', description: 'Lowercase Theta' },
    { char: 'Α', name: 'Alpha', description: 'Uppercase Alpha' },
    { char: 'Β', name: 'Beta', description: 'Uppercase Beta' },
    { char: 'Γ', name: 'Gamma', description: 'Uppercase Gamma' },
    { char: 'Δ', name: 'Delta', description: 'Uppercase Delta' },
    { char: 'Ω', name: 'Omega', description: 'Uppercase Omega' }
  ],
  'Currency': [
    { char: '€', name: 'Euro Sign', description: 'European Union currency' },
    { char: '£', name: 'Pound Sign', description: 'British pound currency' },
    { char: '¥', name: 'Yen Sign', description: 'Japanese yen currency' },
    { char: '₹', name: 'Rupee Sign', description: 'Indian rupee currency' },
    { char: '₽', name: 'Ruble Sign', description: 'Russian ruble currency' },
    { char: '$', name: 'Dollar Sign', description: 'US dollar currency' },
    { char: '¢', name: 'Cent Sign', description: 'Cent currency' },
    { char: '₿', name: 'Bitcoin Sign', description: 'Bitcoin cryptocurrency' },
    { char: '₴', name: 'Hryvnia Sign', description: 'Ukrainian hryvnia currency' },
    { char: '₩', name: 'Won Sign', description: 'Korean won currency' },
    { char: '₫', name: 'Dong Sign', description: 'Vietnamese dong currency' },
    { char: '฿', name: 'Baht Sign', description: 'Thai baht currency' },
    { char: '₱', name: 'Peso Sign', description: 'Philippine peso currency' }
  ],
  'Punctuation': [
    { char: '…', name: 'Ellipsis', description: 'Used to indicate omitted text' },
    { char: '–', name: 'En Dash', description: 'Used for ranges' },
    { char: '—', name: 'Em Dash', description: 'Used for breaks in sentences' },
    { char: '•', name: 'Bullet', description: 'Used for list items' },
    { char: '′', name: 'Prime', description: 'Used for feet or minutes' },
    { char: '″', name: 'Double Prime', description: 'Used for inches or seconds' },
    { char: '«', name: 'Left-Pointing Double Angle Quotation Mark', description: 'Used in quotations' },
    { char: '»', name: 'Right-Pointing Double Angle Quotation Mark', description: 'Used in quotations' },
    { char: '§', name: 'Section Sign', description: 'Used to refer to sections' },
    { char: '¶', name: 'Pilcrow Sign', description: 'Used to denote paragraphs' },
    { char: '‽', name: 'Interrobang', description: 'Combined question and exclamation mark' },
    { char: '©', name: 'Copyright Sign', description: 'Used for copyright notices' },
    { char: '®', name: 'Registered Sign', description: 'Used for registered trademarks' }
  ],
  'Arrows': [
    { char: '←', name: 'Leftwards Arrow', description: 'Points to the left' },
    { char: '→', name: 'Rightwards Arrow', description: 'Points to the right' },
    { char: '↑', name: 'Upwards Arrow', description: 'Points upwards' },
    { char: '↓', name: 'Downwards Arrow', description: 'Points downwards' },
    { char: '↔', name: 'Left Right Arrow', description: 'Points left and right' },
    { char: '↕', name: 'Up Down Arrow', description: 'Points up and down' },
    { char: '⇐', name: 'Leftwards Double Arrow', description: 'Double arrow pointing left' },
    { char: '⇒', name: 'Rightwards Double Arrow', description: 'Double arrow pointing right' },
    { char: '⇑', name: 'Upwards Double Arrow', description: 'Double arrow pointing up' },
    { char: '⇓', name: 'Downwards Double Arrow', description: 'Double arrow pointing down' },
    { char: '⇔', name: 'Left Right Double Arrow', description: 'Double arrow pointing left and right' },
    { char: '⇕', name: 'Up Down Double Arrow', description: 'Double arrow pointing up and down' },
    { char: '↺', name: 'Anticlockwise Open Circle Arrow', description: 'Used for rotation' }
  ]
};

/**
 * Special Character plugin implementation
 * Adds ability to insert special characters and symbols
 */
export class SpecialCharPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Add CSS for symbol picker styles
    this.addSpecialCharStyles();
  }
  
  /**
   * Add CSS styles for special character picker
   */
  private addSpecialCharStyles(): void {
    // Create a style element if it doesn't exist
    let style = document.getElementById('feather-special-char-styles');
    if (!style) {
      style = document.createElement('style');
      style.id = 'feather-special-char-styles';
      document.head.appendChild(style);
      
      // Add the CSS rules
      style.textContent = `
        .feather-special-char-dialog {
          position: fixed;
          width: 400px;
          height: 450px;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          overflow: hidden;
          display: flex;
          flex-direction: column;
          z-index: 9999;
        }
        
        .feather-special-char-header {
          padding: 12px;
          border-bottom: 1px solid #eee;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        
        .feather-special-char-header h3 {
          margin: 0;
          font-size: 16px;
        }
        
        .feather-special-char-close {
          background: none;
          border: none;
          font-size: 18px;
          cursor: pointer;
          padding: 4px;
        }
        
        .feather-special-char-close:hover {
          color: #1a73e8;
        }
        
        .feather-special-char-search {
          padding: 8px 12px;
          border-bottom: 1px solid #eee;
        }
        
        .feather-special-char-search input {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
        }
        
        .feather-special-char-search input:focus {
          border-color: #1a73e8;
          outline: none;
        }
        
        .feather-special-char-tabs {
          display: flex;
          border-bottom: 1px solid #eee;
          overflow-x: auto;
          scrollbar-width: none;
        }
        
        .feather-special-char-tabs::-webkit-scrollbar {
          display: none;
        }
        
        .feather-special-char-tab {
          padding: 8px 12px;
          cursor: pointer;
          background: none;
          border: none;
          border-bottom: 2px solid transparent;
          white-space: nowrap;
        }
        
        .feather-special-char-tab.active {
          border-bottom-color: #1a73e8;
        }
        
        .feather-special-char-grid {
          flex: 1;
          overflow-y: auto;
          padding: 8px;
        }
        
        .feather-special-char-category {
          margin-bottom: 16px;
        }
        
        .feather-special-char-category-title {
          margin: 8px 0;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
        
        .feather-special-char-items {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          gap: 4px;
        }
        
        .feather-special-char-item {
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          cursor: pointer;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        
        .feather-special-char-item:hover {
          background-color: #f1f1f1;
        }
        
        .feather-special-char-preview {
          border-top: 1px solid #eee;
          padding: 12px;
          display: flex;
          flex-direction: column;
          gap: 4px;
        }
        
        .feather-special-char-preview-char {
          font-size: 32px;
          text-align: center;
          color: #1a73e8;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .feather-special-char-preview-info {
          text-align: center;
        }
        
        .feather-special-char-preview-name {
          font-weight: 500;
          font-size: 14px;
        }
        
        .feather-special-char-preview-desc {
          font-size: 12px;
          color: #666;
        }
        
        .feather-special-char-dialog-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 9998;
        }
      `;
    }
  }
  
  /**
   * Handle the special character command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    if (_command === 'special-char' && this.editor) {
      this.openSpecialCharPicker();
    }
  }
  
  /**
   * Open the special character picker dialog
   */
  private openSpecialCharPicker(): void {
    if (this.isDialogOpen) {
      this.closeSpecialCharPicker();
    }
    
    // Get cursor position for placing the picker
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    
    // Create backdrop (invisible, just for capturing clicks outside)
    const backdrop = document.createElement('div');
    backdrop.className = 'feather-special-char-dialog-backdrop';
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'feather-special-char-dialog';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'special-char-dialog-title');
    
    // Position the picker near the cursor
    const pickerWidth = 400;
    const pickerHeight = 450;
    
    let left = rect.left + window.scrollX;
    let top = rect.bottom + window.scrollY + 10;
    
    // Ensure the picker stays within the viewport
    if (left + pickerWidth > window.innerWidth) {
      left = window.innerWidth - pickerWidth - 10;
    }
    
    if (top + pickerHeight > window.innerHeight) {
      top = rect.top + window.scrollY - pickerHeight - 10;
    }
    
    this.dialog.style.left = `${left}px`;
    this.dialog.style.top = `${top}px`;
    
    // Create dialog content
    this.dialog.innerHTML = `
      <div class="feather-special-char-header">
        <h3 id="special-char-dialog-title">Special Characters</h3>
        <button type="button" class="feather-special-char-close" aria-label="Close">×</button>
      </div>
      
      <div class="feather-special-char-search">
        <input type="text" placeholder="Search symbols..." aria-label="Search symbols">
      </div>
      
      <div class="feather-special-char-tabs" role="tablist">
        <button type="button" class="feather-special-char-tab active" data-category="all" role="tab" aria-selected="true">All</button>
        ${Object.keys(symbolData).map(category => `
          <button type="button" class="feather-special-char-tab" data-category="${category}" role="tab" aria-selected="false">${category}</button>
        `).join('')}
      </div>
      
      <div class="feather-special-char-grid" role="tabpanel">
        ${this.renderSymbolGrid('all')}
      </div>
      
      <div class="feather-special-char-preview">
        <div class="feather-special-char-preview-char"></div>
        <div class="feather-special-char-preview-info">
          <div class="feather-special-char-preview-name"></div>
          <div class="feather-special-char-preview-desc"></div>
        </div>
      </div>
    `;
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Set up tab switching
    const tabs = this.dialog.querySelectorAll('.feather-special-char-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Deactivate all tabs
        tabs.forEach(t => {
          t.classList.remove('active');
          t.setAttribute('aria-selected', 'false');
        });
        
        // Activate the clicked tab
        tab.classList.add('active');
        tab.setAttribute('aria-selected', 'true');
        
        // Update the symbol grid
        const category = tab.getAttribute('data-category') || 'all';
        const grid = this.dialog?.querySelector('.feather-special-char-grid');
        if (grid) {
          grid.innerHTML = this.renderSymbolGrid(category);
          
          // Set up hover events for the new grid
          this.setupSymbolHoverEvents();
        }
      });
    });
    
    // Set up search functionality
    const searchInput = this.dialog.querySelector('.feather-special-char-search input') as HTMLInputElement;
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      const grid = this.dialog?.querySelector('.feather-special-char-grid');
      if (grid) {
        grid.innerHTML = this.renderSymbolGrid('all', query);
        
        // Set up hover events for the new grid
        this.setupSymbolHoverEvents();
      }
    });
    
    // Set up symbol hover events
    this.setupSymbolHoverEvents();
    
    // Set up symbol insertion
    this.dialog.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const symbolItem = target.closest('.feather-special-char-item');
      if (symbolItem) {
        const symbol = symbolItem.getAttribute('data-char');
        if (symbol) {
          this.insertSymbol(symbol);
          this.closeSpecialCharPicker();
        }
      }
    });
    
    // Set up close button
    const closeButton = this.dialog.querySelector('.feather-special-char-close');
    closeButton?.addEventListener('click', () => {
      this.closeSpecialCharPicker();
    });
    
    // Close on backdrop click
    backdrop.addEventListener('click', () => {
      this.closeSpecialCharPicker();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Focus the search input
    searchInput.focus();
  }
  
  /**
   * Set up hover events for symbol items
   */
  private setupSymbolHoverEvents(): void {
    if (!this.dialog) return;
    
    const symbolItems = this.dialog.querySelectorAll('.feather-special-char-item');
    const previewChar = this.dialog.querySelector('.feather-special-char-preview-char');
    const previewName = this.dialog.querySelector('.feather-special-char-preview-name');
    const previewDesc = this.dialog.querySelector('.feather-special-char-preview-desc');
    
    symbolItems.forEach(item => {
      item.addEventListener('mouseover', () => {
        const char = item.getAttribute('data-char') || '';
        const name = item.getAttribute('data-name') || '';
        const desc = item.getAttribute('data-desc') || '';
        
        if (previewChar) previewChar.textContent = char;
        if (previewName) previewName.textContent = name;
        if (previewDesc) previewDesc.textContent = desc;
      });
    });
  }
  
  /**
   * Render the symbol grid for a specific category
   * @param category The symbol category to render
   * @param searchQuery Optional search query to filter symbols
   * @returns HTML string for the symbol grid
   */
  private renderSymbolGrid(category: string, searchQuery?: string): string {
    // If search query is provided, filter symbols across all categories
    if (searchQuery) {
      const allSymbols: { category: string; symbol: SymbolItem }[] = [];
      
      // Collect all symbols
      Object.entries(symbolData).forEach(([cat, symbols]) => {
        symbols.forEach(symbol => {
          allSymbols.push({ category: cat, symbol });
        });
      });
      
      // Filter by search query
      const filteredSymbols = allSymbols.filter(item => 
        item.symbol.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.symbol.description && item.symbol.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      
      // Group by category
      const groupedSymbols: Record<string, typeof allSymbols> = {};
      filteredSymbols.forEach(item => {
        if (!groupedSymbols[item.category]) {
          groupedSymbols[item.category] = [];
        }
        groupedSymbols[item.category].push(item);
      });
      
      // Render the filtered symbol grid
      if (Object.keys(groupedSymbols).length === 0) {
        return '<div class="feather-special-char-no-results">No symbols found</div>';
      }
      
      return Object.entries(groupedSymbols)
        .map(([cat, items]) => `
          <div class="feather-special-char-category">
            <div class="feather-special-char-category-title">${cat}</div>
            <div class="feather-special-char-items">
              ${items.map(item => `
                <div 
                  class="feather-special-char-item" 
                  data-char="${item.symbol.char}" 
                  data-name="${item.symbol.name}" 
                  data-desc="${item.symbol.description || ''}" 
                  title="${item.symbol.name}"
                >
                  ${item.symbol.char}
                </div>
              `).join('')}
            </div>
          </div>
        `)
        .join('');
    }
    
    // Render all categories or a specific category
    if (category === 'all') {
      return Object.entries(symbolData)
        .map(([cat, symbols]) => `
          <div class="feather-special-char-category">
            <div class="feather-special-char-category-title">${cat}</div>
            <div class="feather-special-char-items">
              ${symbols.map(symbol => `
                <div 
                  class="feather-special-char-item" 
                  data-char="${symbol.char}" 
                  data-name="${symbol.name}" 
                  data-desc="${symbol.description || ''}" 
                  title="${symbol.name}"
                >
                  ${symbol.char}
                </div>
              `).join('')}
            </div>
          </div>
        `)
        .join('');
    } else {
      const symbols = symbolData[category as keyof typeof symbolData] || [];
      return `
        <div class="feather-special-char-category">
          <div class="feather-special-char-category-title">${category}</div>
          <div class="feather-special-char-items">
            ${symbols.map(symbol => `
              <div 
                class="feather-special-char-item" 
                data-char="${symbol.char}" 
                data-name="${symbol.name}" 
                data-desc="${symbol.description || ''}" 
                title="${symbol.name}"
              >
                ${symbol.char}
              </div>
            `).join('')}
          </div>
        </div>
      `;
    }
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeSpecialCharPicker();
    }
  };
  
  /**
   * Close the special character picker
   */
  private closeSpecialCharPicker(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog and backdrop
    const backdrop = document.querySelector('.feather-special-char-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Insert a symbol at the current cursor position
   * @param symbol The symbol character to insert
   */
  private insertSymbol(symbol: string): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    range.deleteContents();
    
    // Insert the symbol
    const textNode = document.createTextNode(symbol);
    range.insertNode(textNode);
    
    // Move cursor after the symbol
    range.setStartAfter(textNode);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close picker if open
    if (this.isDialogOpen) {
      this.closeSpecialCharPicker();
    }
  }
}

// Create and export the plugin instance directly
const plugin = new SpecialCharPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
