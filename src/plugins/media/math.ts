/**
 * Math Plugin (∑) – KaTeX via NPM
 */


import { BasePlugin, PluginConfig } from '../base-plugin';
import { ThemeManager } from '../../themes/theme-manager-legacy';

/* ---- import KaTeX CSS once so it ends up in the bundle ---- */
import 'katex/dist/katex.min.css';

/* -------------------------------------------------------------------------- */
/*                               Plugin meta                                  */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id:          'math',
  name:        'Math',
  description: 'Inline / block KaTeX equations',
  version:     '2.0.0',
  toolbarItems: [
    {
      id: 'math',
      command: 'math',
      icon: '∑',
      label: 'Math',
      tooltip: 'Insert math equation',
      group: 'special',
      ariaLabel: 'Insert math equation'
    }
  ]
};

export const pluginId = META.id;

/* -------------------------------------------------------------------------- */
/*                           Config & defaults                                */
/* -------------------------------------------------------------------------- */

interface MathPluginConfig {
  inlineDelimiter?: [string, string];   // default $ $
  blockDelimiter?:  [string, string];   // default $$ $$
}

const DEFAULTS: Required<MathPluginConfig> = {
  inlineDelimiter: ['$', '$'],
  blockDelimiter : ['$$', '$$']
};

/* -------------------------------------------------------------------------- */
/*                        Lazy-load KaTeX (bundler)                            */
/* -------------------------------------------------------------------------- */

let katexPromise: Promise<typeof import('katex')>;
function getKatex(): Promise<typeof import('katex')> {
  return (katexPromise ??= import('katex'));
}

/* -------------------------------------------------------------------------- */
/*                               Main plugin                                  */
/* -------------------------------------------------------------------------- */

class MathPlugin extends BasePlugin {
  private cfg: ReturnType<typeof Object.assign>;
  private abort = new AbortController();

  constructor(userCfg: MathPluginConfig = {}) {
    super(META);
    this.cfg = Object.assign({}, DEFAULTS, userCfg);
  }

  /* -------------------------- Base hooks ----------------------------- */

  protected override onInit(): void {
    /* render whenever the editor changes */
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.addEventListener(
        'input',
        () => this.renderMathAsync(),
        { signal: this.abort.signal }
      );
    }

    /* listen for theme changes to update math elements */
    window.addEventListener('feather:themechange', () => this.updateMathTheme(), {
      signal: this.abort.signal
    });

    // Also listen for the document-level themechange event
    document.addEventListener('themechange', () => this.updateMathTheme(), {
      signal: this.abort.signal
    });

    queueMicrotask(() => this.renderMathAsync());
  }

  /**
   * Update theme classes on all math elements when theme changes
   */
  private updateMathTheme(): void {
    if (!this.editor) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Find all math elements in the editor
    const mathElements = editorElement.querySelectorAll('.math-inline-wrapper, .math-display-wrapper, .katex-container');

    // Update theme classes on each math element
    mathElements.forEach(element => {
      if (element instanceof HTMLElement) {
        // Apply theme to the element
        ThemeManager.applyThemeToElement(element);
      }
    });

    // Re-render math to ensure proper styling
    this.renderMathAsync();
  }

  public override handleCommand(command: string): void {
    if (command === 'math') this.openEquationDialog();
  }

  /* --------------------------- Renderer ------------------------------ */

  private async renderMathAsync(): Promise<void> {
    if (!this.editor) return;
    const katex = await getKatex();

    /* Convert delimiters to spans */
    const { inlineDelimiter, blockDelimiter } = this.cfg;

    const inlineRe = new RegExp(
      `${escapeRegex(inlineDelimiter[0])}([^\\n${inlineDelimiter[1]}]+?)${escapeRegex(
        inlineDelimiter[1]
      )}`,
      'g'
    );
    const blockRe = new RegExp(
      `${escapeRegex(blockDelimiter[0])}([\\s\\S]+?)${escapeRegex(
        blockDelimiter[1]
      )}`,
      'g'
    );

    /* Walk text nodes */
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    const walker = document.createTreeWalker(
      editorElement,
      NodeFilter.SHOW_TEXT
    );
    const nodes: Text[] = [];
    let n: Node | null;
    while ((n = walker.nextNode())) nodes.push(n as Text);

    nodes.forEach((textNode) => {
      const parent = textNode.parentElement;
      if (
        parent?.classList.contains('feather-math-inline') ||
        parent?.classList.contains('feather-math-display')
      )
        return;

      const html = textNode.data
        .replace(blockRe, (_, expr) =>
          spanHTML('display', escapeHtml(expr.trim()))
        )
        .replace(inlineRe, (_, expr) =>
          spanHTML('inline', escapeHtml(expr.trim()))
        );
      if (html !== textNode.data) {
        const fragment =
          document.createRange().createContextualFragment(html);
        textNode.replaceWith(fragment);
      }
    });

    /* Render each span */
    const unrendered = editorElement.querySelectorAll<
      HTMLSpanElement
    >(
      '.feather-math-inline:not([data-rendered]),' +
        '.feather-math-display:not([data-rendered])'
    );
    unrendered.forEach((span) => {
      try {
        katex.render(span.textContent ?? '', span, {
          displayMode: span.classList.contains('math-display-wrapper') // Check for new display class
        });
        span.dataset.rendered = 'true';
        // Clear any error styling if successful
        span.classList.remove('bg-red-100', 'dark:bg-red-900/50', 'text-red-700', 'dark:text-red-400', 'font-mono', 'whitespace-pre-wrap', 'p-1');
        span.style.backgroundColor = ''; // Clear direct style if any was set by KaTeX error
        span.style.color = '';

        // Apply theme classes to the rendered math element
        ThemeManager.applyThemeToElement(span);

        // Also apply theme to any KaTeX-generated elements inside
        span.querySelectorAll('.katex, .katex-html').forEach(element => {
          if (element instanceof HTMLElement) {
            ThemeManager.applyThemeToElement(element);
          }
        });
      } catch (err) {
        // Apply Tailwind error classes instead of just 'feather-math-error'
        span.classList.add('bg-red-100', 'dark:bg-red-900/50', 'text-red-700', 'dark:text-red-400', 'font-mono', 'whitespace-pre-wrap', 'p-1');
        span.title = String(err);

        // Still apply theme classes even to error elements
        ThemeManager.applyThemeToElement(span);
      }
    });
  }

  /* ----------------------- Insert equation UI ------------------------ */

  private openEquationDialog(): void {
    if (!this.editor) return;

    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-black/40 z-[1000]';

    const dialog = document.createElement('div');
    dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 p-4 rounded-lg z-[1001] w-[90%] max-w-md flex flex-col gap-2.5 shadow-lg';

    const title = document.createElement('h3');
    title.className = 'mt-0 mb-1.5 text-lg font-semibold text-gray-900 dark:text-slate-100';
    title.textContent = 'Add equation';
    dialog.appendChild(title);

    const input = document.createElement('textarea');
    input.rows = 3;
    input.className = 'w-full font-mono text-sm leading-snug border border-gray-300 dark:border-slate-600 rounded p-2 min-h-[80px] box-border resize-y bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    dialog.appendChild(input);

    const labelCb = document.createElement('label');
    labelCb.className = 'flex items-center text-sm text-gray-700 dark:text-slate-300 cursor-pointer';
    const displayCb = document.createElement('input');
    displayCb.type = 'checkbox';
    displayCb.className = 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-slate-700 dark:border-slate-600 mr-2';
    labelCb.appendChild(displayCb);
    labelCb.appendChild(document.createTextNode(' Display (block)'));
    dialog.appendChild(labelCb);

    const preview = document.createElement('div');
    preview.className = 'min-h-[40px] p-2.5 border border-dashed border-gray-300 dark:border-slate-600 rounded bg-gray-50 dark:bg-slate-800/50 overflow-x-auto';
    dialog.appendChild(preview);

    const footer = document.createElement('footer');
    footer.className = 'flex justify-end gap-2 mt-1.5';

    const cancelButton = document.createElement('button');
    cancelButton.type = 'button';
    cancelButton.className = 'py-1.5 px-3.5 rounded border border-gray-300 dark:border-slate-600 cursor-pointer bg-gray-100 dark:bg-slate-700 text-gray-800 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-600 text-sm';
    cancelButton.textContent = 'Cancel';
    footer.appendChild(cancelButton);

    const insertButton = document.createElement('button');
    insertButton.type = 'button';
    insertButton.className = 'py-1.5 px-3.5 rounded cursor-pointer bg-blue-600 hover:bg-blue-500 text-white border-blue-600 hover:border-blue-500 text-sm';
    insertButton.textContent = 'Insert';
    footer.appendChild(insertButton);
    dialog.appendChild(footer);

    // const input = dialog.querySelector<HTMLTextAreaElement>( // No longer needed with direct references
    //   '.feather-math-input'
    // ); // No longer needed
    // const preview = dialog.querySelector<HTMLDivElement>( // No longer needed
    //   '.feather-math-preview'
    // )!; // No longer needed
    // const displayCb = dialog.querySelector<HTMLInputElement>( // No longer needed
    //   '.feather-math-block-cb'
    // )!; // No longer needed

    /* live preview */
    const updatePreview = async () => {
      preview.innerHTML = '';
      if (!input.value.trim()) return;
      try {
        const katex = await getKatex();
        katex.render(input.value, preview, {
          displayMode: displayCb.checked
        });
      } catch (err) {
        preview.textContent = String(err);
      }
    };
    input.addEventListener('input', updatePreview);
    displayCb.addEventListener('change', updatePreview);

    /* insert */
    insertButton.addEventListener('click', () => {
        const expr = input.value.trim();
        if (!expr) return;
        const delimiters = displayCb.checked
          ? this.cfg.blockDelimiter
          : this.cfg.inlineDelimiter;
        const snippet = `${delimiters[0]}${expr}${delimiters[1]}`;

        const sel = window.getSelection();
        if (sel && sel.rangeCount) {
          sel.deleteFromDocument();
          sel.getRangeAt(0).insertNode(document.createTextNode(snippet));
        } else {
          const editorElement = this.editor?.getElement();
          if (editorElement) {
            editorElement.append(snippet);
          }
        }

        backdrop.remove();
        dialog.remove();
        this.renderMathAsync();
      });

    cancelButton.addEventListener('click', () => {
        backdrop.remove();
        dialog.remove();
      });

    document.body.append(backdrop, dialog);
    input.focus();
  }

  /* ------------------------------ Destroy --------------------------- */

  public override destroy(): void {
    this.abort.abort();
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                               Factory export                               */
/* -------------------------------------------------------------------------- */

// Create and export the plugin instance directly
const plugin = new MathPlugin();
export default plugin;

/* -------------------------------------------------------------------------- */
/*                         Small utility helpers                              */
/* -------------------------------------------------------------------------- */

function escapeRegex(str: string): string {
  return str.replace(/[$()*+?.\\^|{}[\]-]/g, '\\$&');
}
function escapeHtml(raw: string): string {
  return raw.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
}
function spanHTML(type: 'inline' | 'display', tex: string): string {
  const baseClasses = 'katex-container'; // Common marker if needed, KaTeX might add its own
  let typeClasses = '';
  if (type === 'inline') {
    typeClasses = 'math-inline-wrapper whitespace-nowrap bg-gray-50 dark:bg-slate-800 px-1 py-0.5 rounded-sm border border-gray-200 dark:border-slate-700 text-sm';
  } else { // display
    typeClasses = 'math-display-wrapper block bg-gray-50 dark:bg-slate-800 py-2 px-4 my-2 rounded border border-gray-200 dark:border-slate-700 overflow-x-auto';
  }

  // We'll add theme classes after the element is created using ThemeManager
  // This ensures we're using the user's theme preference rather than system preference
  return `<span class="${baseClasses} ${typeClasses}">${tex}</span>`;
}

