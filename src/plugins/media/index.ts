import type { Plugin } from '../../types';
// Import all media plugins
import linkPlugin from './link';
import imagePlugin from './image';
import videoPlugin from './video';
import audioPlugin from './audio';
import attachmentPlugin from './attachment';
import emojiPlugin from './emoji';
import specialCharPlugin from './special-char';
import chartPlugin from './chart';
import mathPlugin from './math';
import snippetPlugin from './snippet';

// This file serves as the entry point for all media plugins
// Each plugin is exported individually to ensure tree-shaking works properly

// Export individual plugins
export {
  linkPlugin,
  imagePlugin,
  videoPlugin,
  audioPlugin,
  attachmentPlugin,
  emojiPlugin,
  specialCharPlugin,
  chartPlugin,
  mathPlugin,
  snippetPlugin
};

// Export a combined plugin group for convenience
export const MediaPlugins: Plugin[] = [
  linkPlugin,
  imagePlugin,
  videoPlugin,
  audioPlugin,
  attachmentPlugin,
  emojiPlugin,
  specialCharPlugin,
  chartPlugin,
  mathPlugin,
  snippetPlugin
];

// Export the plugins array directly
// PluginManager will handle individual plugin registration
export default MediaPlugins;
