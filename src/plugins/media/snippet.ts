/**
 * Snippet Plugin for FeatherJS  📄
 * ───────────────────────────────
 * • “Add Snippet” captures the current selection.
 * • A side panel lists all saved snippets; click ➕ to insert at the caret.
 * • Snippets are stored in localStorage (key = `feather.snippets`).
 * • Long descriptive class names, rich inline comments, no third-party libs.
 */


import { BasePlugin, PluginConfig } from '../base-plugin';
/* -------------------------------------------------------------------------- */
/*                               Metadata                                     */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id:          'snippet',
  name:        'Snippet',
  description: 'Reusable content blocks',
  version:     '1.0.0',
  toolbarItems: [
    {
      id: 'snippet',
      command: 'snippet',
      icon: '📄',
      label: 'Snippets',
      tooltip: 'Toggle snippet panel',
      group: 'special',
      ariaLabel: 'Toggle snippet panel'
    }
  ]
};

export const pluginId = META.id;

/* -------------------------------------------------------------------------- */
/*                         Storage + data typing                              */
/* -------------------------------------------------------------------------- */

interface SnippetRecord {
  id: string;
  name: string;
  html: string;
  created: number;
}

const STORAGE_KEY = 'feather.snippets';

/* -------------------------------------------------------------------------- */

class SnippetPlugin extends BasePlugin {
  /* -------------------- fields -------------------- */

  private panel?: HTMLElement;
  private isOpen = false;
  private snippets: SnippetRecord[] = [];
  private abort = new AbortController();

  constructor() {
    super(META);
  }

  /* -------------------- init --------------------- */

  protected override onInit(): void {
    this.loadSnippetsFromStorage();
  }

  /* ------------ toolbar / commands ------------- */

  public override handleCommand(cmd: string): void {
    if (cmd === 'snippet') this.togglePanel();
  }

  /* ------------------- panel -------------------- */

  private togglePanel(): void {
    if (this.isOpen) {
      this.closePanel();
    } else {
      this.openPanel();
    }
  }

  private openPanel(): void {
    if (!this.editor || this.panel) return;

    this.panel = document.createElement('aside');
    this.panel.className = 'absolute top-0 right-0 bottom-0 w-[260px] bg-white dark:bg-slate-800 border-l border-gray-300 dark:border-slate-700 shadow-[-2px_0_5px_rgba(0,0,0,0.05)] flex flex-col font-sans text-sm z-[90] text-gray-900 dark:text-slate-200';

    const header = document.createElement('header');
    header.className = 'flex justify-between items-center px-3 py-2.5 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-700/50 flex-shrink-0';

    const title = document.createElement('h3');
    title.className = 'm-0 text-base font-semibold';
    title.textContent = 'Snippets';
    header.appendChild(title);

    const headerActions = document.createElement('div');
    headerActions.className = 'flex items-center'; // Adjusted from feather-snippet-header-actions

    const addButton = document.createElement('button');
    addButton.className = 'feather-snippet-add ml-1.5 border border-gray-300 dark:border-slate-600 bg-gray-100 dark:bg-slate-700 px-2 py-[3px] rounded cursor-pointer text-gray-700 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-600 text-xs';
    addButton.title = 'Create from selection';
    addButton.textContent = '➕';
    headerActions.appendChild(addButton);

    const closeButton = document.createElement('button');
    closeButton.className = 'feather-snippet-close ml-1.5 border border-gray-300 dark:border-slate-600 bg-gray-100 dark:bg-slate-700 px-2 py-[3px] rounded cursor-pointer text-gray-700 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-600 text-xs';
    closeButton.title = 'Close panel';
    closeButton.textContent = '✕';
    headerActions.appendChild(closeButton);

    header.appendChild(headerActions);
    this.panel.appendChild(header);

    const listContainer = document.createElement('div');
    listContainer.className = 'feather-snippet-list flex-1 overflow-y-auto p-2.5';
    this.panel.appendChild(listContainer);

    /* Action buttons */
    closeButton
      .addEventListener('click', () => this.closePanel());
    this.panel
      .querySelector('.feather-snippet-add')!
      .addEventListener('click', () => this.createSnippetFromSelection());

    this.renderSnippetList();

    /* attach beside editor */
    const editorElement = this.editor.getElement();
    if (editorElement?.parentElement) {
      editorElement.parentElement.appendChild(this.panel);
    }
    this.isOpen = true;
  }

  private closePanel(): void {
    this.panel?.remove();
    this.panel = undefined;
    this.isOpen = false;
  }

  /* ---------------- snippet CRUD --------------- */

  private createSnippetFromSelection(): void {
    if (!this.editor) return;
    const sel = window.getSelection();
    if (!sel || sel.isCollapsed) {
      alert('Select some content first.');
      return;
    }

    const name = prompt('Snippet name:', `Snippet ${this.snippets.length + 1}`);
    if (!name) return;

    const range = sel.getRangeAt(0);
    const container = document.createElement('div');
    container.appendChild(range.cloneContents());

    const snippet: SnippetRecord = {
      id: `s-${Date.now()}`,
      name,
      html: container.innerHTML,
      created: Date.now()
    };
    this.snippets.push(snippet);
    this.saveSnippetsToStorage();
    this.renderSnippetList();
  }

  private deleteSnippet(id: string): void {
    this.snippets = this.snippets.filter((s) => s.id !== id);
    this.saveSnippetsToStorage();
    this.renderSnippetList();
  }

  /* ------------------ render ------------------- */

  private renderSnippetList(): void {
    if (!this.panel) return;
    const list = this.panel.querySelector('.feather-snippet-list')!;
    list.innerHTML = ''; // Clear existing items

    if (this.snippets.length === 0) {
      const emptyMessage = document.createElement('p');
      emptyMessage.className = 'text-gray-500 dark:text-slate-400 italic p-4 text-center';
      emptyMessage.textContent = 'No snippets yet. Select text and click ➕.';
      list.appendChild(emptyMessage);
      return;
    }

    this.snippets
      .sort((a, b) => b.created - a.created)
      .forEach((snip) => {
        const item = document.createElement('div');
        item.className = 'flex justify-between items-center border border-gray-200 dark:border-slate-700 rounded p-2 mb-2 bg-white dark:bg-slate-800/50 transition-colors duration-150 ease-in-out hover:bg-gray-100 dark:hover:bg-slate-700';

        const nameSpan = document.createElement('span');
        nameSpan.className = 'flex-grow overflow-hidden text-ellipsis whitespace-nowrap mr-2.5';
        nameSpan.textContent = snip.name;
        item.appendChild(nameSpan);

        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'flex-shrink-0';

        const insertButton = document.createElement('button');
        insertButton.className = 'border-none bg-transparent cursor-pointer ml-1.5 p-0.5 text-lg text-gray-600 dark:text-slate-400 hover:text-black dark:hover:text-white';
        insertButton.title = 'Insert snippet';
        insertButton.textContent = '↩︎';
        actionsDiv.appendChild(insertButton);

        const deleteButton = document.createElement('button');
        deleteButton.className = 'border-none bg-transparent cursor-pointer ml-1.5 p-0.5 text-lg text-gray-600 dark:text-slate-400 hover:text-black dark:hover:text-white';
        deleteButton.title = 'Delete snippet';
        deleteButton.textContent = '🗑';
        actionsDiv.appendChild(deleteButton);

        item.appendChild(actionsDiv);

        /* preview tooltip (HTML rendered) */
        item.title = stripTags(snip.html);

        /* insert */
        insertButton.addEventListener('click', () => {
            if (!this.editor) return;
            const sel = window.getSelection();

            if (sel && sel.rangeCount) {
              const range = sel.getRangeAt(0);
              range.deleteContents(); // Clear existing selection

              // Insert the snippet HTML
              const fragment = range.createContextualFragment(snip.html);
              range.insertNode(fragment);

              // Move caret after inserted content
              range.collapse(false);
              sel.removeAllRanges();
              sel.addRange(range);
            } else {
              // Fallback: append to the end if no selection
              const editorElement = this.editor.getElement();
              if (editorElement) {
                editorElement.insertAdjacentHTML('beforeend', snip.html);
              }
            }
            this.closePanel(); // Close panel after inserting
          });

        /* delete */
        deleteButton.addEventListener('click', () => {
            if (confirm(`Delete snippet “${snip.name}”?`))
              this.deleteSnippet(snip.id);
          });

        list.appendChild(item);
      });
  }

  /* --------------- localStorage I/O ----------- */

  private loadSnippetsFromStorage(): void {
    try {
      const raw = localStorage.getItem(STORAGE_KEY);
      if (raw) this.snippets = JSON.parse(raw);
    } catch {
      this.snippets = [];
    }
  }

  private saveSnippetsToStorage(): void {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(this.snippets));
  }

  /* ------------------- destroy ----------------- */

  public override destroy(): void {
    this.abort.abort();
    this.closePanel();
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                               Factory                                      */
/* -------------------------------------------------------------------------- */

// Create and export the plugin instance directly
const plugin = new SnippetPlugin();

export default plugin;

/* -------------------------------------------------------------------------- */
/*                          Utility helpers                                   */
/* -------------------------------------------------------------------------- */

function stripTags(html: string): string {
  const divEl = document.createElement('div');
  divEl.innerHTML = html;
  return divEl.textContent ?? '';
}
