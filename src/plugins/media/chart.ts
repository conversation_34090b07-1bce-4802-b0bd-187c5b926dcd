/**
 * Chart Plugin for Feather JS  📊
 * ──────────────────────────────
 * • Converts pasted CSV / JSON datasets into interactive Chart.js graphs.
 * • Supports <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.
 * • Charts are persisted as  <canvas class="feather-chart"
 *     data-chart="{...ChartJS config...}"></canvas>
 *   … so re-opening the doc re-hydrates the graph without server calls.
 * • Everything is type-safe (Chart.js @types) and listeners are cleaned up
 *   with AbortController.
 */


import { BasePlugin, PluginConfig } from '../base-plugin';
import { ThemeManager } from '../../themes/theme-manager-legacy';

// Chart.js doesn't directly export CSS, we'll handle styling in our own CSS
// Create a dedicated CSS file for chart styling if needed in the future

/* -------------------------------------------------------------------------- */
/*                               Plugin meta                                  */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id:          'chart',
  name:        'Chart',
  description: 'CSV/JSON → interactive charts',
  version:     '1.0.0',
  toolbarItems: [
    {
      id: 'chart',
      command: 'chart',
      icon: '📊',
      label: 'Chart',
      tooltip: 'Insert chart from data',
      group: 'special',
      ariaLabel: 'Insert chart'
    }
  ]
};

export const pluginId = META.id;

/* -------------------------------------------------------------------------- */
/*                              Config typing                                 */
/* -------------------------------------------------------------------------- */

type ChartKind =
  | 'bar'
  | 'line'
  | 'pie'
  | 'doughnut'
  | 'radar'
  | 'polarArea'
  | 'scatter'
  | 'bubble';

interface ChartPluginConfig {
  defaultType?: ChartKind;
  defaultColors?: string[];
}

const DEFAULTS: Required<ChartPluginConfig> = {
  defaultType: 'bar',
  defaultColors: [
    '#42a5f5',
    '#66bb6a',
    '#ffa726',
    '#ef5350',
    '#ab47bc',
    '#26c6da',
    '#8d6e63',
    '#d4e157'
  ]
};

/* -------------------------------------------------------------------------- */
/*                   Lazy import – bundles only when needed                   */
/* -------------------------------------------------------------------------- */

type ChartJS = typeof import('chart.js/auto');
let chartJsPromise: Promise<ChartJS>;
async function getChartJs(): Promise<ChartJS> {
  chartJsPromise ??= import('chart.js/auto');
  return chartJsPromise;
}

/* -------------------------------------------------------------------------- */
/*                              The plugin class                              */
/* -------------------------------------------------------------------------- */

class ChartPlugin extends BasePlugin {
  private cfg: Required<ChartPluginConfig>;
  private abort = new AbortController();

  /** every live Chart.js instance so we can destroy on cleanup */
  private readonly charts = new Map<HTMLCanvasElement, import('chart.js').Chart>();

  constructor(userCfg: ChartPluginConfig = {}) {
    super(META);
    this.cfg = Object.assign({}, DEFAULTS, userCfg);
  }

  /* ------------------------- BasePlugin hooks ------------------------- */

  protected override onInit(): void {
    /* Render any saved charts already in the document */
    queueMicrotask(() => this.hydrateCharts());

    /* If user pastes CSV or JSON, try to auto-detect & prompt */
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.addEventListener(
        'paste',
        (ev) => this.handlePaste(ev as ClipboardEvent),
        { signal: this.abort.signal }
      );
    }

    /* listen for theme changes to update all charts */
    window.addEventListener('feather:themechange', () => this.updateChartsTheme(), {
      signal: this.abort.signal
    });

    // Also listen for the document-level themechange event
    document.addEventListener('themechange', () => this.updateChartsTheme(), {
      signal: this.abort.signal
    });
  }

  /**
   * Update theme classes on all charts when theme changes
   */
  private updateChartsTheme(): void {
    if (!this.editor) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Find all chart canvases in the editor
    const chartCanvases = editorElement.querySelectorAll('canvas.feather-chart');

    // Update theme classes on each chart
    chartCanvases.forEach(canvas => {
      if (canvas instanceof HTMLCanvasElement) {
        // Apply theme to the canvas
        ThemeManager.applyThemeToElement(canvas);

        // Get the chart instance
        const chart = this.charts.get(canvas);
        if (chart) {
          // Get the chart configuration
          const config = chart.config;

          // Destroy the chart
          chart.destroy();

          // Recreate the chart with the updated theme
          getChartJs().then(lib => {
            this.createChartInstance(canvas, config as import('chart.js').ChartConfiguration, lib);
          });
        }
      }
    });
  }

  public override handleCommand(command: string): void {
    if (command === 'chart') this.openChartDialog();
  }

  /* -------------------------- Paste detector -------------------------- */

  private async handlePaste(event: ClipboardEvent): Promise<void> {
    if (!event.clipboardData) return;
    const text = event.clipboardData.getData('text/plain');
    if (!text) return;
    if (isProbablyCSV(text) || isProbablyJSON(text)) {
      event.preventDefault();
      /* keep clipboard text in case user cancels */
      this.openChartDialog(text);
    }
  }

  /* --------------------------- Main dialog ---------------------------- */

  private async openChartDialog(seedData = ''): Promise<void> {
    const dialog = document.createElement('div');
    dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 z-[1001] w-[90%] max-w-[520px] rounded-lg p-4 flex flex-col gap-2.5 font-sans shadow-lg text-gray-900 dark:text-slate-200';

    const title = document.createElement('h3');
    title.className = 'mt-0 mb-2.5 text-lg font-semibold';
    title.textContent = 'Create Chart';
    dialog.appendChild(title);

    const textarea = document.createElement('textarea');
    textarea.className = 'w-full min-h-[120px] font-mono text-sm leading-snug border border-gray-300 dark:border-slate-600 rounded p-1.5 box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    textarea.placeholder = 'Paste CSV or JSON';
    textarea.value = seedData;
    dialog.appendChild(textarea);

    const optionsContainer = document.createElement('div');
    optionsContainer.className = 'flex gap-2.5 items-center';
    const typeLabel = document.createElement('label');
    typeLabel.className = 'text-sm text-gray-700 dark:text-slate-300';
    typeLabel.textContent = 'Chart type:';
    optionsContainer.appendChild(typeLabel);

    const typeSelect = document.createElement('select');
    typeSelect.className = 'py-1 px-2 border border-gray-300 dark:border-slate-600 rounded text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    (['bar','line','pie','doughnut','radar','polarArea','scatter','bubble'] as ChartKind[]).forEach(t => {
      const option = document.createElement('option');
      option.value = t;
      option.textContent = t;
      if (t === this.cfg.defaultType) option.selected = true;
      typeSelect.appendChild(option);
    });
    optionsContainer.appendChild(typeSelect);
    dialog.appendChild(optionsContainer);

    const previewContainer = document.createElement('div');
    previewContainer.className = 'relative border border-gray-200 dark:border-slate-700 rounded p-1.5 min-h-[240px] bg-gray-50 dark:bg-slate-800/50 mt-2.5';
    const previewCanvas = document.createElement('canvas');
    previewCanvas.className = 'max-w-full block'; // feather-chart-preview
    previewCanvas.height = 240;
    previewContainer.appendChild(previewCanvas);

    const errorDisplay = document.createElement('div');
    errorDisplay.className = 'absolute inset-0 p-3 text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-900/30 font-mono whitespace-pre-wrap z-10 flex items-center justify-center text-center rounded hidden';
    previewContainer.appendChild(errorDisplay);
    dialog.appendChild(previewContainer);

    const footer = document.createElement('footer');
    footer.className = 'flex justify-end gap-2 mt-2.5';
    const cancelButton = document.createElement('button');
    cancelButton.type = 'button';
    cancelButton.className = 'py-1.5 px-3.5 rounded border border-gray-300 dark:border-slate-600 cursor-pointer bg-gray-100 dark:bg-slate-700 text-gray-800 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-600 text-sm';
    cancelButton.textContent = 'Cancel';
    footer.appendChild(cancelButton);
    const insertButton = document.createElement('button');
    insertButton.type = 'button';
    insertButton.className = 'py-1.5 px-3.5 rounded cursor-pointer bg-blue-600 hover:bg-blue-500 text-white border-blue-600 hover:border-blue-500 text-sm';
    insertButton.textContent = 'Insert Chart';
    footer.appendChild(insertButton);
    dialog.appendChild(footer);

    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-black/40 z-[1000]';
    document.body.append(backdrop, dialog);

    const chartJs = await getChartJs();
    let previewChart: import('chart.js').Chart | undefined;

    const renderPreview = () => {
      const raw = textarea.value.trim();
      let config: import('chart.js').ChartConfiguration | null = null;
      errorDisplay.classList.add('hidden'); // Hide error by default
      errorDisplay.textContent = '';

      try {
        config = this.convertDataToConfig(raw, typeSelect.value as ChartKind);
      } catch (err) {
        errorDisplay.textContent = String(err);
        errorDisplay.classList.remove('hidden');
      }

      previewChart?.destroy(); // Destroy previous chart instance

      if (!config) {
        previewCanvas.style.display = 'none'; // Hide canvas if no config
        return;
      }
      previewCanvas.style.display = 'block'; // Show canvas

      previewChart = new chartJs.Chart(previewCanvas, config);
    };

    textarea.addEventListener('input', renderPreview);
    typeSelect.addEventListener('change', renderPreview);

    renderPreview(); // initial

    /* cancel / close */
    cancelButton.addEventListener('click', () => {
        previewChart?.destroy();
        backdrop.remove();
        dialog.remove();
      });

    /* insert */
    insertButton.addEventListener('click', async () => {
        const raw = textarea.value.trim();
        const chartType = typeSelect.value as ChartKind;
        let config: import('chart.js').ChartConfiguration | null = null;
        try {
          config = this.convertDataToConfig(raw, chartType);
        } catch (err) {
          alert(err);
          return;
        }
        if (!config) return; // Check if config is null

        /* Embed */
        if (!this.editor) return;

        const canvas = document.createElement('canvas');
        canvas.className = 'feather-chart block max-w-full my-4 mx-auto border border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900 rounded';
        canvas.height = 240; // Consider making this configurable or responsive
        canvas.dataset.chart = JSON.stringify(config);

        /* place at selection */
        const sel = window.getSelection();
        if (sel && sel.rangeCount) {
          sel.deleteFromDocument();
          sel.getRangeAt(0).insertNode(canvas);
        } else {
          this.editor.getElement().appendChild(canvas);
        }

        /* hydrate immediately */
        await this.insertChart(config);

        /* close dialog */
        previewChart?.destroy();
        backdrop.remove();
        dialog.remove();
      });
  }

  // Restore convertDataToConfig method
  private convertDataToConfig(
    raw: string,
    kind: ChartKind
  ): import('chart.js').ChartConfiguration {
    type AnyData =
      | number
      | [number, number]
      | import('chart.js').ScatterDataPoint
      | import('chart.js').BubbleDataPoint
      | null;

    const colorCycle = this.cfg.defaultColors;
    let labels: string[] = [];
    let datasets: import('chart.js').ChartDataset<import('chart.js').ChartType, AnyData[]>[] = [];

    if (isProbablyJSON(raw)) {
      /* user supplied full structure - assumes compatible with 'kind' */
      try {
        const obj = JSON.parse(raw) as {
          labels?: string[];
          datasets: { label: string; data: AnyData[]; backgroundColor?: string }[];
        };
        labels = obj.labels ?? []; // Use provided labels or empty array
        datasets = obj.datasets.map((d, i) => ({
          ...d,
          backgroundColor: d.backgroundColor ?? colorCycle[i % colorCycle.length]
        }));
      } catch (e) {
        throw new Error(`Invalid JSON format: ${(e as Error).message}`);
      }
    } else {
      /* treat as CSV */
      const rows = raw
        .split(/\r?\n/)
        .filter(Boolean)
        .map((r) => r.split(',').map(v => v.trim())); // Trim values

      if (rows.length < 2)
        throw new Error('CSV requires a header row and at least one data row');

      const headers = rows[0];
      const dataRows = rows.slice(1);

      // Determine label column (usually the first)
      labels = dataRows.map((r) => r[0] ?? ''); // Use first column for labels
      const datasetHeaders = headers.slice(1);

      datasets = datasetHeaders.map((header, hIdx) => {
        const datasetIndex = hIdx + 1; // Column index in the original CSV
        let data: AnyData[] = [];

        switch (kind) {
          case 'scatter':
            // Expect pairs of columns: X, Y for each dataset header
            if (datasetIndex + 1 >= headers.length) throw new Error(`Scatter plot needs X, Y columns for '${header}'`);
            data = dataRows.map(r => ({
              x: Number(r[datasetIndex]),
              y: Number(r[datasetIndex + 1])
            } as import('chart.js').ScatterDataPoint));
            // Skip the next header as it was consumed as Y
            // Note: This simple mapping assumes headers correctly represent datasets.
            // A more robust parser might group columns.
            break;

          case 'bubble':
            // Expect triples: X, Y, R (radius) for each dataset header
            if (datasetIndex + 2 >= headers.length) throw new Error(`Bubble chart needs X, Y, R columns for '${header}'`);
             data = dataRows.map(r => ({
               x: Number(r[datasetIndex]),
               y: Number(r[datasetIndex + 1]),
               r: Number(r[datasetIndex + 2])
             } as import('chart.js').BubbleDataPoint));
            // Skip the next two headers
            break;

          // Bar, Line, Pie, Doughnut, Radar, PolarArea typically use single numeric values
          default:
             data = dataRows.map(r => {
               const val = r[datasetIndex];
               // Return null for non-numeric or empty strings to avoid NaN issues
               return (val === '' || isNaN(Number(val))) ? null : Number(val);
             });
            break;
        }

        return {
          label: header,
          backgroundColor: colorCycle[hIdx % colorCycle.length],
          borderColor: colorCycle[hIdx % colorCycle.length], // Often needed too
          data: data,
        };
      });

        // Adjust dataset iteration for scatter/bubble where headers were consumed
        if (kind === 'scatter') {
            datasets = datasets.filter((_, i) => i % 2 === 0); // Keep only X headers
        } else if (kind === 'bubble') {
            datasets = datasets.filter((_, i) => i % 3 === 0); // Keep only X headers
        }
    }

    // Final check for empty datasets which can cause Chart.js errors
    if (datasets.length === 0 || datasets.every(ds => ds.data.length === 0)) {
        throw new Error('No valid data could be parsed to create datasets.');
    }

    return {
      type: kind as import('chart.js').ChartType, // Keep assertion for type mapping
      data: { labels, datasets },
      options: { responsive: true, maintainAspectRatio: false }
    } as import('chart.js').ChartConfiguration; // Keep assertion for overall structure
  }

  /* --------------------------- Chart insert/hydrate --------------------- */

  // Make async to await getChartJs
  private async insertChart(
    config: import('chart.js').ChartConfiguration
  ): Promise<void> {
    if (!this.editor) return;

    const canvas = document.createElement('canvas');
    canvas.width = 400; // Default size
    canvas.height = 200;

    const wrapper = div('feather-chart-wrapper');
    wrapper.contentEditable = 'false';
    wrapper.dataset.chartConfig = JSON.stringify(config);
    wrapper.appendChild(canvas);

    // Add a non-editable line break after to prevent merging
    const breakEl = document.createElement('br');
    breakEl.setAttribute('contenteditable', 'false');

    // Get the ChartJS library
    const chartJs = await getChartJs();

    // Insert elements
    const selection = window.getSelection();
    const range = selection?.rangeCount ? selection.getRangeAt(0) : null;

    if (range) {
      range.deleteContents();
      range.insertNode(wrapper);
      // Insert the break after the wrapper
      wrapper.after(breakEl);
      // Move selection after the break
      range.setStartAfter(breakEl);
      range.collapse(true);
      // Use window.getSelection() API
      selection?.removeAllRanges();
      selection?.addRange(range);
    } else {
      // Fallback if no range/selection
      this.editor?.getElement()?.appendChild(wrapper);
      this.editor?.getElement()?.appendChild(breakEl);
    }

    // Pass the library instance
    this.createChartInstance(canvas, config, chartJs);
  }

  /* ----------------------- Chart hydration --------------------------- */

  private async hydrateCharts(): Promise<void> {
    if (!this.editor) return;
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    const chartJs = await getChartJs();

    const canvases = editorElement.querySelectorAll<HTMLCanvasElement>(
      'canvas.feather-chart:not([data-hydrated])'
    );
    canvases.forEach((canvas) => {
      const raw = canvas.dataset.chart;
      if (!raw) return;
      try {
        const config = JSON.parse(raw);
        this.createChartInstance(canvas, config, chartJs);
      } catch {
        /* ignore bad data */
      }
    });
  }

  private createChartInstance(
    canvas: HTMLCanvasElement,
    config: import('chart.js').ChartConfiguration,
    lib: ChartJS
  ): void {
    const chartJs = lib;
    if (!chartJs) return;

    // Apply theme classes to the canvas
    ThemeManager.applyThemeToElement(canvas);

    // Get current theme from ThemeManager
    const currentTheme = ThemeManager.getCurrentTheme();
    const isDark = currentTheme === 'dark';

    // Update chart colors based on theme
    if (config.options && typeof config.options === 'object') {
      if (!config.options.plugins) {
        config.options.plugins = {};
      }

      // Set theme-aware colors
      if (isDark) {
        // Set dark theme colors
        if (!config.options.scales) {
          config.options.scales = {};
        }

        // Set grid and text colors for dark theme using CSS variables
        const scales = config.options.scales as any;
        if (scales.x) {
          scales.x.grid = { ...scales.x.grid, color: 'rgba(255, 255, 255, 0.1)' };
          scales.x.ticks = { ...scales.x.ticks, color: 'rgba(255, 255, 255, 0.7)' };
        }
        if (scales.y) {
          scales.y.grid = { ...scales.y.grid, color: 'rgba(255, 255, 255, 0.1)' };
          scales.y.ticks = { ...scales.y.ticks, color: 'rgba(255, 255, 255, 0.7)' };
        }

        // Set legend text color for dark theme
        if (!config.options.plugins.legend) {
          config.options.plugins.legend = {};
        }
        config.options.plugins.legend.labels = {
          ...config.options.plugins.legend.labels,
          color: 'rgba(255, 255, 255, 0.9)'
        };
      } else {
        // Set light theme colors
        if (!config.options.scales) {
          config.options.scales = {};
        }

        // Set grid and text colors for light theme
        const scales = config.options.scales as any;
        if (scales.x) {
          scales.x.grid = { ...scales.x.grid, color: 'rgba(0, 0, 0, 0.1)' };
          scales.x.ticks = { ...scales.x.ticks, color: 'rgba(0, 0, 0, 0.7)' };
        }
        if (scales.y) {
          scales.y.grid = { ...scales.y.grid, color: 'rgba(0, 0, 0, 0.1)' };
          scales.y.ticks = { ...scales.y.ticks, color: 'rgba(0, 0, 0, 0.7)' };
        }

        // Set legend text color for light theme
        if (!config.options.plugins.legend) {
          config.options.plugins.legend = {};
        }
        config.options.plugins.legend.labels = {
          ...config.options.plugins.legend.labels,
          color: 'rgba(0, 0, 0, 0.9)'
        };
      }
    }

    // Ensure config types are strictly adhered to, Chart.js can be picky
    const chart = new chartJs.Chart(canvas, config);
    this.charts.set(canvas, chart);
    canvas.dataset.hydrated = 'true';

    // Listen for theme changes
    const themeChangeListener = (_e: Event) => {
      // Get the current chart instance
      const chart = this.charts.get(canvas);
      if (!chart) return;

      // Theme change detected - we don't need to use the specific theme value
      // since we'll get the current theme from ThemeManager

      // Apply theme to canvas
      ThemeManager.applyThemeToElement(canvas);

      // Destroy and recreate the chart with updated theme
      chart.destroy();
      this.createChartInstance(canvas, config, lib);
    };

    // Store the listener reference for cleanup
    canvas.dataset.themeListener = 'true';
    document.addEventListener('themechange', themeChangeListener);
  }

  /* ----------------------------- Cleanup ----------------------------- */

  public override destroy(): void {
    this.abort.abort();
    this.charts.forEach((c) => c.destroy());
    this.charts.clear();
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                                Factory                                     */
/* -------------------------------------------------------------------------- */

// Create and export the plugin instance directly
const plugin = new ChartPlugin();
export default plugin;

/* -------------------------------------------------------------------------- */
/*                             Helper utilities                               */
/* -------------------------------------------------------------------------- */

function isProbablyCSV(txt: string): boolean {
  return /^[^,\n]+(,[^,\n]+)+/m.test(txt.trim());
}
function isProbablyJSON(txt: string): boolean {
  return txt.trim().startsWith('{') || txt.trim().startsWith('[');
}
function div(cls: string): HTMLDivElement {
  return Object.assign(document.createElement('div'), { className: cls });
}
