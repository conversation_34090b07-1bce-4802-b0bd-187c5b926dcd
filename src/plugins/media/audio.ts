import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

/**
 * Audio plugin configuration
 */
const config: PluginConfig = {
  id: 'audio',
  name: 'Audio',
  description: 'Insert and manage audio players',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'audio',
      command: 'audio',
      icon: '🎵',
      label: 'Audio',
      tooltip: 'Insert audio player',
      group: 'media',
      ariaLabel: 'Insert audio player',
    }
  ],
  shortcuts: [] // No keyboard shortcuts for audio to avoid conflicts
};

/**
 * Audio plugin implementation
 * Adds ability to embed audio players with customizable controls
 */
export class AudioPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the audio command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    if (_command === 'audio' && this.editor) {
      this.openAudioDialog();
    }
  }
  
  /**
   * Open the audio insert/edit dialog
   * @param existingAudioWrapper Existing audio wrapper to edit
   */
  private openAudioDialog(existingAudioWrapper: HTMLElement | null = null): void {
    if (this.isDialogOpen) {
      this.closeAudioDialog();
    }
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/50 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 min-w-[400px] max-w-[80vw] z-[9999]'; // Tailwind for dialog
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'audio-dialog-title');
    
    // Get current values if editing an existing audio
    let currentSrc = '';
    let currentTitle = '';
    let currentCaption = '';
    let currentAutoplay = false;
    let currentLoop = false;
    
    if (existingAudioWrapper) {
      const audio = existingAudioWrapper.querySelector('audio');
      if (audio) {
        currentSrc = audio.src;
        currentAutoplay = audio.autoplay;
        currentLoop = audio.loop;
      }
      
      const title = existingAudioWrapper.querySelector('.feather-audio-title');
      if (title) {
        currentTitle = title.textContent || '';
      }
      
      const caption = existingAudioWrapper.querySelector('.feather-audio-caption');
      if (caption) {
        currentCaption = caption.textContent || '';
      }
    }
    
    // Create dialog content programmatically
    const dialogTitle = document.createElement('h3');
    dialogTitle.id = 'audio-dialog-title';
    dialogTitle.className = 'text-lg font-semibold mt-0 mb-4 text-gray-900 dark:text-slate-100';
    dialogTitle.textContent = `${existingAudioWrapper ? 'Edit' : 'Insert'} Audio`;
    this.dialog.appendChild(dialogTitle);

    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'flex border-b border-gray-300 dark:border-slate-700 mb-4';
    
    const urlTabButton = document.createElement('button');
    urlTabButton.type = 'button';
    urlTabButton.className = 'px-4 py-2 -mb-px border-b-2 font-medium text-sm focus:outline-none border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400'; // Active tab
    urlTabButton.dataset.tab = 'url';
    urlTabButton.textContent = 'URL';
    
    const uploadTabButton = document.createElement('button');
    uploadTabButton.type = 'button';
    uploadTabButton.className = 'px-4 py-2 -mb-px border-b-2 font-medium text-sm focus:outline-none text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 border-transparent hover:border-gray-300 dark:hover:border-slate-600'; // Inactive tab
    uploadTabButton.dataset.tab = 'upload';
    uploadTabButton.textContent = 'Upload';

    tabsContainer.appendChild(urlTabButton);
    tabsContainer.appendChild(uploadTabButton);
    this.dialog.appendChild(tabsContainer);

    const createField = (labelText: string, inputId: string, inputType: string, placeholder: string, value: string) => {
      const div = document.createElement('div');
      const label = document.createElement('label') as HTMLLabelElement;
      label.htmlFor = inputId;
      label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
      label.textContent = labelText;
      const input = document.createElement('input');
      input.type = inputType; input.id = inputId;
      input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 text-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
      input.placeholder = placeholder; input.value = value;
      div.appendChild(label); div.appendChild(input);
      return div;
    };
    
    const createCheckboxOption = (optionId: string, optionLabel: string, checked: boolean) => {
      const div = document.createElement('div');
      const input = document.createElement('input');
      input.type = 'checkbox'; input.id = optionId; input.checked = checked;
      input.className = 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600';
      const label = document.createElement('label') as HTMLLabelElement;
      label.htmlFor = optionId;
      label.className = 'ml-2 text-sm font-normal text-gray-700 dark:text-slate-300';
      label.textContent = optionLabel;
      div.appendChild(input); div.appendChild(label);
      return div;
    };

    // URL Panel
    const urlPanel = document.createElement('div');
    urlPanel.className = 'block'; // Active by default
    urlPanel.dataset.panel = 'url';
    const urlForm = document.createElement('form');
    urlForm.className = 'flex flex-col gap-3';
    urlForm.appendChild(createField('Audio URL', 'audio-url', 'url', 'https://example.com/audio.mp3', currentSrc));
    urlForm.appendChild(createField('Title', 'audio-title', 'text', 'Audio title', currentTitle));
    urlForm.appendChild(createField('Caption (optional)', 'audio-caption', 'text', 'Audio description', currentCaption));
    const urlOptionsDiv = document.createElement('div');
    const urlOptionsLabel = document.createElement('label');
    urlOptionsLabel.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    urlOptionsLabel.textContent = 'Options';
    urlOptionsDiv.appendChild(urlOptionsLabel);
    const urlOptionsInnerDiv = document.createElement('div');
    urlOptionsInnerDiv.className = 'flex gap-4 mt-1';
    urlOptionsInnerDiv.appendChild(createCheckboxOption('audio-autoplay', 'Autoplay', currentAutoplay));
    urlOptionsInnerDiv.appendChild(createCheckboxOption('audio-loop', 'Loop', currentLoop));
    urlOptionsDiv.appendChild(urlOptionsInnerDiv);
    urlForm.appendChild(urlOptionsDiv);
    urlPanel.appendChild(urlForm);
    this.dialog.appendChild(urlPanel);

    // Upload Panel
    const uploadPanel = document.createElement('div');
    uploadPanel.className = 'hidden'; // Hidden by default
    uploadPanel.dataset.panel = 'upload';
    const uploadForm = document.createElement('form');
    uploadForm.className = 'flex flex-col gap-3';
    const dropzoneDiv = document.createElement('div');
    dropzoneDiv.id = 'audio-dropzone';
    dropzoneDiv.className = 'border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-md p-8 text-center mb-4 cursor-pointer hover:border-blue-500 dark:hover:border-blue-400';
    const dropzoneP = document.createElement('p');
    dropzoneP.className = 'text-gray-500 dark:text-slate-400';
    dropzoneP.textContent = 'Drag an audio file here or click to upload';
    dropzoneDiv.appendChild(dropzoneP);
    const fileInputElem = document.createElement('input');
    fileInputElem.type = 'file'; fileInputElem.id = 'audio-file'; fileInputElem.accept = 'audio/*'; fileInputElem.className = 'hidden';
    dropzoneDiv.appendChild(fileInputElem);
    uploadForm.appendChild(dropzoneDiv);
    uploadForm.appendChild(createField('Title', 'audio-title-upload', 'text', 'Audio title', currentTitle));
    uploadForm.appendChild(createField('Caption (optional)', 'audio-caption-upload', 'text', 'Audio description', currentCaption));
    const uploadOptionsDiv = document.createElement('div');
    const uploadOptionsLabel = document.createElement('label');
    uploadOptionsLabel.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    uploadOptionsLabel.textContent = 'Options';
    uploadOptionsDiv.appendChild(uploadOptionsLabel);
    const uploadOptionsInnerDiv = document.createElement('div');
    uploadOptionsInnerDiv.className = 'flex gap-4 mt-1';
    uploadOptionsInnerDiv.appendChild(createCheckboxOption('audio-autoplay-upload', 'Autoplay', currentAutoplay));
    uploadOptionsInnerDiv.appendChild(createCheckboxOption('audio-loop-upload', 'Loop', currentLoop));
    uploadOptionsDiv.appendChild(uploadOptionsInnerDiv);
    uploadForm.appendChild(uploadOptionsDiv);
    uploadPanel.appendChild(uploadForm);
    this.dialog.appendChild(uploadPanel);

    // Buttons Container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'flex justify-end gap-2 mt-6 pt-4 border-t border-gray-200 dark:border-slate-700';
    
    if (existingAudioWrapper) {
      const removeButtonElem = document.createElement('button');
      removeButtonElem.type = 'button'; removeButtonElem.id = 'audio-remove-button';
      removeButtonElem.className = 'py-2 px-4 rounded font-medium bg-red-500 hover:bg-red-600 text-white';
      removeButtonElem.textContent = 'Remove Audio';
      buttonsContainer.appendChild(removeButtonElem);
    }

    const cancelButtonElem = document.createElement('button');
    cancelButtonElem.type = 'button'; cancelButtonElem.id = 'audio-cancel-button';
    cancelButtonElem.className = 'py-2 px-4 rounded font-medium bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200 border border-gray-300 dark:border-slate-500';
    cancelButtonElem.textContent = 'Cancel';
    buttonsContainer.appendChild(cancelButtonElem);

    const insertButtonElem = document.createElement('button');
    insertButtonElem.type = 'button'; insertButtonElem.id = 'audio-insert-button';
    insertButtonElem.className = 'py-2 px-4 rounded font-medium bg-blue-600 hover:bg-blue-700 text-white';
    insertButtonElem.textContent = existingAudioWrapper ? 'Update' : 'Insert';
    buttonsContainer.appendChild(insertButtonElem);
    
    this.dialog.appendChild(buttonsContainer);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Set up tab switching
    const tabs = this.dialog.querySelectorAll('.feather-audio-dialog-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Deactivate all tabs and panels
        tabs.forEach(t => t.classList.remove('active'));
        const panels = this.dialog?.querySelectorAll('.feather-audio-dialog-panel');
        panels?.forEach(p => p.classList.remove('active'));
        
        // Activate the clicked tab and its panel
        tab.classList.add('active');
        const panelId = tab.getAttribute('data-tab');
        const panel = this.dialog?.querySelector(`.feather-audio-dialog-panel[data-panel="${panelId}"]`);
        panel?.classList.add('active');
      });
    });
    
    // Set up file upload
    const dropzone = this.dialog.querySelector('#audio-dropzone');
    const fileInput = this.dialog.querySelector('#audio-file') as HTMLInputElement;
    
    dropzone?.addEventListener('click', () => {
      fileInput?.click();
    });
    
    dropzone?.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropzone.classList.add('dragover');
    });
    
    dropzone?.addEventListener('dragleave', () => {
      dropzone.classList.remove('dragover');
    });
    
    dropzone?.addEventListener('drop', (e: Event) => {
      const dragEvent = e as DragEvent;
      dragEvent.preventDefault();
      dragEvent.stopPropagation();
      dropzone.classList.remove('dragover');
      
      if (dragEvent.dataTransfer?.files.length) {
        fileInput.files = dragEvent.dataTransfer.files;
        
        // Update the dropzone text with the filename
        const filename = dragEvent.dataTransfer.files[0].name;
        const nameInput = this.dialog?.querySelector('#audio-title-upload') as HTMLInputElement;
        if (nameInput && !nameInput.value) {
          nameInput.value = filename;
        }
        
        const dropzoneText = dropzone?.querySelector('p');
        if (dropzoneText) {
          dropzoneText.textContent = `Selected: ${filename}`;
        }
      }
    });
    
    fileInput?.addEventListener('change', () => {
      // Visual feedback that file was selected
      if (fileInput.files?.length) {
        const filename = fileInput.files[0].name;
        const dropzoneText = dropzone?.querySelector('p');
        if (dropzoneText) {
          dropzoneText.textContent = `Selected: ${filename}`;
        }
      }
    });
    
    // Set up button actions
    const cancelButton = this.dialog.querySelector('#audio-cancel-button');
    const insertButton = this.dialog.querySelector('#audio-insert-button');
    const removeButton = this.dialog.querySelector('#audio-remove-button');
    
    cancelButton?.addEventListener('click', () => this.closeAudioDialog());
    backdrop.addEventListener('click', () => this.closeAudioDialog());
    
    insertButton?.addEventListener('click', () => {
      const activePanel = this.dialog?.querySelector('.feather-audio-dialog-panel.active');
      const panelId = activePanel?.getAttribute('data-panel');
      
      if (panelId === 'url') {
        // Get values from URL form
        const url = (this.dialog?.querySelector('#audio-url') as HTMLInputElement)?.value || '';
        const title = (this.dialog?.querySelector('#audio-title') as HTMLInputElement)?.value || '';
        const caption = (this.dialog?.querySelector('#audio-caption') as HTMLInputElement)?.value || '';
        const autoplay = (this.dialog?.querySelector('#audio-autoplay') as HTMLInputElement)?.checked || false;
        const loop = (this.dialog?.querySelector('#audio-loop') as HTMLInputElement)?.checked || false;
        
        if (url) {
          if (existingAudioWrapper) {
            // Update existing audio
            this.updateAudio(existingAudioWrapper, url, title, caption, autoplay, loop);
          } else {
            // Insert new audio
            this.insertAudio(url, title, caption, autoplay, loop);
          }
        }
      } else if (panelId === 'upload') {
        // Get values from upload form
        const fileInput = this.dialog?.querySelector('#audio-file') as HTMLInputElement;
        const title = (this.dialog?.querySelector('#audio-title-upload') as HTMLInputElement)?.value || '';
        const caption = (this.dialog?.querySelector('#audio-caption-upload') as HTMLInputElement)?.value || '';
        const autoplay = (this.dialog?.querySelector('#audio-autoplay-upload') as HTMLInputElement)?.checked || false;
        const loop = (this.dialog?.querySelector('#audio-loop-upload') as HTMLInputElement)?.checked || false;
        
        if (fileInput?.files?.length) {
          const file = fileInput.files[0];
          this.handleAudioUpload(file, title, caption, autoplay, loop, existingAudioWrapper);
        }
      }
      
      this.closeAudioDialog();
    });
    
    if (removeButton && existingAudioWrapper) {
      removeButton.addEventListener('click', () => {
        this.removeAudio(existingAudioWrapper);
        this.closeAudioDialog();
      });
    }
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Focus the URL input if showing URL tab
    const urlInput = this.dialog.querySelector('#audio-url') as HTMLInputElement;
    if (urlInput) {
      urlInput.focus();
    }
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeAudioDialog();
    }
  };
  
  /**
   * Close the audio dialog
   */
  private closeAudioDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog and backdrop
    const backdrop = document.querySelector('.feather-audio-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Insert a new audio player
   * @param src The audio source URL
   * @param title The audio title
   * @param caption The audio caption
   * @param autoplay Whether to autoplay
   * @param loop Whether to loop
   */
  private insertAudio(
    src: string,
    title: string = '',
    caption: string = '',
    autoplay: boolean = false,
    loop: boolean = false
  ): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    
    // Create audio wrapper
    const wrapper = document.createElement('div');
    wrapper.className = 'group relative w-full my-4 max-w-2xl p-4 bg-gray-50 dark:bg-slate-800 rounded-lg border border-gray-200 dark:border-slate-700';
    
    // Add title if provided
    if (title) {
      const titleElement = document.createElement('div');
      titleElement.className = 'font-medium mb-2 text-gray-800 dark:text-slate-100';
      titleElement.textContent = title;
      wrapper.appendChild(titleElement);
    }
    
    // Create the audio element
    const audio = document.createElement('audio');
    audio.className = 'w-full'; // Tailwind for audio element
    audio.src = src;
    audio.controls = true;
    audio.autoplay = autoplay;
    audio.loop = loop;
    audio.preload = 'metadata';
    
    // Create controls container
    const controls = document.createElement('div');
    controls.className = 'absolute top-2 right-2 z-10 hidden group-hover:flex group-hover:gap-1';
    
    // Create edit button control
    const editControl = document.createElement('div');
    editControl.className = 'bg-white dark:bg-slate-600 rounded-full w-6 h-6 flex items-center justify-center cursor-pointer shadow-md text-gray-700 dark:text-slate-200 hover:bg-gray-100 dark:hover:bg-slate-500';
    editControl.setAttribute('aria-label', 'Edit audio');
    editControl.setAttribute('title', 'Edit');
    editControl.innerHTML = '🖊'; // Using text emoji, consider SVG for consistency
    
    editControl.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent wrapper click if any
      this.openAudioDialog(wrapper);
    });
    controls.appendChild(editControl);
    
    // Add the audio element to the wrapper
    wrapper.appendChild(audio);
    wrapper.appendChild(controls);
    
    // Add caption if provided
    if (caption) {
      const captionElement = document.createElement('div');
      captionElement.className = 'mt-2 text-gray-500 dark:text-slate-400 text-sm text-center';
      captionElement.textContent = caption;
      wrapper.appendChild(captionElement);
    }
    
    // Delete any selected content
    range.deleteContents();
    
    // Insert the audio wrapper
    range.insertNode(wrapper);
    
    // Move cursor after the audio
    selection.removeAllRanges();
    const newRange = document.createRange();
    newRange.setStartAfter(wrapper);
    newRange.collapse(true);
    selection.addRange(newRange);
    
    // Trigger input event for history
    const editorElement = this.editor.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Update an existing audio player
   * @param wrapperElement The audio wrapper element
   * @param src The new source URL
   * @param title The new title
   * @param caption The new caption
   * @param autoplay Whether to autoplay
   * @param loop Whether to loop
   */
  private updateAudio(
    wrapperElement: HTMLElement,
    src: string,
    title: string = '',
    caption: string = '',
    autoplay: boolean = false,
    loop: boolean = false
  ): void {
    if (!this.editor) return;
    
    // Update audio src and options
    const audio = wrapperElement.querySelector('audio');
    if (audio) {
      audio.src = src;
      audio.autoplay = autoplay;
      audio.loop = loop;
    }
    
    // Update title
    let titleElement = wrapperElement.querySelector('.feather-audio-title');
    if (title) {
      if (titleElement) {
        titleElement.textContent = title;
      } else {
        titleElement = document.createElement('div');
        titleElement.className = 'feather-audio-title';
        titleElement.textContent = title;
        wrapperElement.insertBefore(titleElement, wrapperElement.firstChild);
      }
    } else if (titleElement) {
      titleElement.parentNode?.removeChild(titleElement);
    }
    
    // Update caption
    let captionElement = wrapperElement.querySelector('.feather-audio-caption');
    if (caption) {
      if (captionElement) {
        captionElement.textContent = caption;
      } else {
        captionElement = document.createElement('div');
        captionElement.className = 'feather-audio-caption';
        captionElement.textContent = caption;
        wrapperElement.appendChild(captionElement);
      }
    } else if (captionElement) {
      captionElement.parentNode?.removeChild(captionElement);
    }
    
    // Trigger input event for history
    const editorElement = this.editor.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Remove an audio player
   * @param wrapperElement The audio wrapper element
   */
  private removeAudio(wrapperElement: HTMLElement): void {
    if (!this.editor) return;
    
    if (wrapperElement.parentNode) {
      wrapperElement.parentNode.removeChild(wrapperElement);
      
      // Trigger input event for history
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
      }
    }
  }
  
  /**
   * Handle audio file upload
   * @param file The audio file to upload
   * @param title The audio title
   * @param caption The audio caption
   * @param autoplay Whether to autoplay
   * @param loop Whether to loop
   * @param existingAudioWrapper Existing audio wrapper to update
   */
  private handleAudioUpload(
    file: File,
    title: string = '',
    caption: string = '',
    autoplay: boolean = false,
    loop: boolean = false,
    existingAudioWrapper: HTMLElement | null = null
  ): void {
    // Use FileReader to handle the audio file
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const src = e.target?.result as string;
      
      if (existingAudioWrapper) {
        this.updateAudio(existingAudioWrapper, src, title, caption, autoplay, loop);
      } else {
        this.insertAudio(src, title, caption, autoplay, loop);
      }
    };
    
    reader.readAsDataURL(file);
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeAudioDialog();
    }
  }
}

// Create and export the plugin instance directly
const plugin = new AudioPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
