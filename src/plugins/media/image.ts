import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

/**
 * Image plugin configuration
 */
const config: PluginConfig = {
  id: 'image',
  name: 'Image',
  description: 'Insert and manage images',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'image',
      command: 'image',
      icon: '🖼',
      label: 'Image',
      tooltip: 'Insert image (Ctrl/⌘+Shift+I)',
      group: 'media',
      ariaLabel: 'Insert image',
    }
  ],
  shortcuts: [
    {
      command: 'image',
      key: 'i',
      ctrlKey: true,
      shiftKey: true,
      description: 'Insert image'
    }
  ]
};

/**
 * Image plugin implementation
 * Handles image insertion from URL, upload, drag & drop, and paste
 */
export class ImagePlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private pasteHandler: (e: ClipboardEvent) => void;
  private dragOverHandler: (e: DragEvent) => void;
  private dropHandler: (e: DragEvent) => void;
  
  constructor() {
    super(config);
    
    // Create bound methods for event listeners
    this.pasteHandler = this.handlePaste.bind(this);
    this.dragOverHandler = this.handleDragOver.bind(this);
    this.dropHandler = this.handleDrop.bind(this);
  }
  
  /**
   * Initialize the plugin and set up event handlers
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Set up event handlers for paste, drag & drop
    if (this.editor) {
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.addEventListener('paste', this.pasteHandler);
        editorElement.addEventListener('dragover', this.dragOverHandler);
        editorElement.addEventListener('drop', this.dropHandler);
      }
    }
  }
  
  /**
   * Handle the image command
   * @param _command The command to handle
   */
  public handleCommand(_command: string): void {
    if (_command === 'image' && this.editor) {
      this.openImageDialog();
    }
  }
  
  /**
   * Open the image insert/edit dialog
   * @param existingImage Existing image element to edit
   */
  private openImageDialog(existingImage: HTMLImageElement | null = null): void {
    if (this.isDialogOpen) {
      this.closeImageDialog();
    }
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/50 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 min-w-[400px] max-w-[80vw] z-[9999]'; // Tailwind for dialog
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'image-dialog-title');
    
    // Get current values if editing an existing image
    const currentSrc = existingImage?.src || '';
    const currentAlt = existingImage?.alt || '';
    const currentWidth = existingImage?.width ? `${existingImage.width}` : '';
    const currentHeight = existingImage?.height ? `${existingImage.height}` : '';
    
    // Create dialog content programmatically
    const dialogTitle = document.createElement('h3');
    dialogTitle.id = 'image-dialog-title';
    dialogTitle.className = 'text-lg font-semibold mt-0 mb-4 text-gray-900 dark:text-slate-100';
    dialogTitle.textContent = `${existingImage ? 'Edit' : 'Insert'} Image`;
    this.dialog.appendChild(dialogTitle);

    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'flex border-b border-gray-300 dark:border-slate-700 mb-4';
    
    const urlTabButton = document.createElement('button');
    urlTabButton.type = 'button';
    urlTabButton.className = 'px-4 py-2 -mb-px border-b-2 font-medium text-sm focus:outline-none border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400'; // Active tab
    urlTabButton.dataset.tab = 'url';
    urlTabButton.textContent = 'URL';
    
    const uploadTabButton = document.createElement('button');
    uploadTabButton.type = 'button';
    uploadTabButton.className = 'px-4 py-2 -mb-px border-b-2 font-medium text-sm focus:outline-none text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 border-transparent hover:border-gray-300 dark:hover:border-slate-600'; // Inactive tab
    uploadTabButton.dataset.tab = 'upload';
    uploadTabButton.textContent = 'Upload';

    tabsContainer.appendChild(urlTabButton);
    tabsContainer.appendChild(uploadTabButton);
    this.dialog.appendChild(tabsContainer);

    const createField = (labelText: string, inputId: string, inputType: string, placeholder: string, value: string, parentForm: HTMLElement) => {
      const div = document.createElement('div');
      const label = document.createElement('label') as HTMLLabelElement;
      label.htmlFor = inputId;
      label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
      label.textContent = labelText;
      const input = document.createElement('input');
      input.type = inputType; input.id = inputId;
      input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 text-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
      input.placeholder = placeholder; input.value = value;
      div.appendChild(label); div.appendChild(input);
      parentForm.appendChild(div);
    };
    
    const createDimensionFields = (idPrefix: string, widthVal: string, heightVal: string, parentForm: HTMLElement) => {
      const dimContainer = document.createElement('div');
      dimContainer.className = 'flex gap-2'; // Tailwind for dimensions container
      
      const widthDiv = document.createElement('div');
      widthDiv.className = 'flex-1';
      createField('Width (px)', `${idPrefix}-width`, 'number', 'Auto', widthVal, widthDiv);
      
      const heightDiv = document.createElement('div');
      heightDiv.className = 'flex-1';
      createField('Height (px)', `${idPrefix}-height`, 'number', 'Auto', heightVal, heightDiv);
      
      dimContainer.appendChild(widthDiv);
      dimContainer.appendChild(heightDiv);
      parentForm.appendChild(dimContainer);
    };

    // URL Panel
    const urlPanel = document.createElement('div');
    urlPanel.className = 'block'; // Active by default
    urlPanel.dataset.panel = 'url';
    const urlForm = document.createElement('form');
    urlForm.className = 'flex flex-col gap-3';
    createField('Image URL', 'image-url', 'url', 'https://example.com/image.jpg', currentSrc, urlForm);
    createField('Alt Text', 'image-alt', 'text', 'Image description', currentAlt, urlForm);
    createDimensionFields('image', currentWidth, currentHeight, urlForm);
    urlPanel.appendChild(urlForm);
    this.dialog.appendChild(urlPanel);

    // Upload Panel
    const uploadPanel = document.createElement('div');
    uploadPanel.className = 'hidden'; // Hidden by default
    uploadPanel.dataset.panel = 'upload';
    const uploadForm = document.createElement('form');
    uploadForm.className = 'flex flex-col gap-3';
    const dropzoneDiv = document.createElement('div');
    dropzoneDiv.id = 'image-dropzone';
    dropzoneDiv.className = 'border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-md p-8 text-center mb-4 cursor-pointer hover:border-blue-500 dark:hover:border-blue-400';
    const dropzoneP = document.createElement('p');
    dropzoneP.className = 'text-gray-500 dark:text-slate-400';
    dropzoneP.textContent = 'Drag an image here or click to upload';
    dropzoneDiv.appendChild(dropzoneP);
    const fileInputElem = document.createElement('input');
    fileInputElem.type = 'file'; fileInputElem.id = 'image-file'; fileInputElem.accept = 'image/*'; fileInputElem.className = 'hidden';
    dropzoneDiv.appendChild(fileInputElem);
    uploadForm.appendChild(dropzoneDiv);
    createField('Alt Text', 'image-alt-upload', 'text', 'Image description', currentAlt, uploadForm);
    createDimensionFields('image-upload', currentWidth, currentHeight, uploadForm);
    uploadPanel.appendChild(uploadForm);
    this.dialog.appendChild(uploadPanel);

    // Buttons Container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'flex justify-end gap-2 mt-6 pt-4 border-t border-gray-200 dark:border-slate-700';
    
    if (existingImage) {
      const removeButtonElem = document.createElement('button');
      removeButtonElem.type = 'button'; removeButtonElem.id = 'image-remove-button';
      removeButtonElem.className = 'py-2 px-4 rounded font-medium bg-red-500 hover:bg-red-600 text-white';
      removeButtonElem.textContent = 'Remove Image';
      buttonsContainer.appendChild(removeButtonElem);
    }

    const cancelButtonElem = document.createElement('button');
    cancelButtonElem.type = 'button'; cancelButtonElem.id = 'image-cancel-button';
    cancelButtonElem.className = 'py-2 px-4 rounded font-medium bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200 border border-gray-300 dark:border-slate-500';
    cancelButtonElem.textContent = 'Cancel';
    buttonsContainer.appendChild(cancelButtonElem);

    const insertButtonElem = document.createElement('button');
    insertButtonElem.type = 'button'; insertButtonElem.id = 'image-insert-button';
    insertButtonElem.className = 'py-2 px-4 rounded font-medium bg-blue-600 hover:bg-blue-700 text-white';
    insertButtonElem.textContent = existingImage ? 'Update' : 'Insert';
    buttonsContainer.appendChild(insertButtonElem);
    
    this.dialog.appendChild(buttonsContainer);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Set up tab switching
    const tabs = this.dialog.querySelectorAll('.feather-image-dialog-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Deactivate all tabs and panels
        tabs.forEach(t => t.classList.remove('active'));
        const panels = this.dialog?.querySelectorAll('.feather-image-dialog-panel');
        panels?.forEach(p => p.classList.remove('active'));
        
        // Activate the clicked tab and its panel
        tab.classList.add('active');
        const panelId = tab.getAttribute('data-tab');
        const panel = this.dialog?.querySelector(`.feather-image-dialog-panel[data-panel="${panelId}"]`);
        panel?.classList.add('active');
      });
    });
    
    // Set up file upload
    const dropzone = this.dialog.querySelector('#image-dropzone');
    const fileInput = this.dialog.querySelector('#image-file') as HTMLInputElement;
    
    dropzone?.addEventListener('click', () => {
      fileInput?.click();
    });
    
    dropzone?.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropzone.classList.add('dragover');
    });
    
    dropzone?.addEventListener('dragleave', () => {
      dropzone.classList.remove('dragover');
    });
    
    dropzone?.addEventListener('drop', (e: Event) => {
      const dragEvent = e as DragEvent;
      dragEvent.preventDefault();
      dragEvent.stopPropagation();
      dropzone.classList.remove('dragover');
      
      if (dragEvent.dataTransfer?.files.length) {
        fileInput.files = dragEvent.dataTransfer.files;
        
        // Update the dropzone text with the filename
        const filename = dragEvent.dataTransfer.files[0].name;
        const nameInput = this.dialog?.querySelector('#image-title-upload') as HTMLInputElement;
        if (nameInput && !nameInput.value) {
          nameInput.value = filename;
        }
        
        const dropzoneText = dropzone?.querySelector('p');
        if (dropzoneText) {
          dropzoneText.textContent = `Selected: ${filename}`;
        }
      }
    });
    
    fileInput?.addEventListener('change', () => {
      // Visual feedback that file was selected
      if (fileInput.files?.length) {
        const filename = fileInput.files[0].name;
        const dropzoneText = dropzone?.querySelector('p');
        if (dropzoneText) {
          dropzoneText.textContent = `Selected: ${filename}`;
        }
      }
    });
    
    // Set up button actions
    const cancelButton = this.dialog.querySelector('#image-cancel-button');
    const insertButton = this.dialog.querySelector('#image-insert-button');
    const removeButton = this.dialog.querySelector('#image-remove-button');
    
    cancelButton?.addEventListener('click', () => this.closeImageDialog());
    backdrop.addEventListener('click', () => this.closeImageDialog());
    
    insertButton?.addEventListener('click', () => {
      const activePanel = this.dialog?.querySelector('.feather-image-dialog-panel.active');
      const panelId = activePanel?.getAttribute('data-panel');
      
      if (panelId === 'url') {
        // Get values from URL form
        const url = (this.dialog?.querySelector('#image-url') as HTMLInputElement)?.value || '';
        const alt = (this.dialog?.querySelector('#image-alt') as HTMLInputElement)?.value || '';
        const width = (this.dialog?.querySelector('#image-width') as HTMLInputElement)?.value || '';
        const height = (this.dialog?.querySelector('#image-height') as HTMLInputElement)?.value || '';
        
        if (url) {
          if (existingImage) {
            // Update existing image
            this.updateImage(existingImage, url, alt, width, height);
          } else {
            // Insert new image
            this.insertImage(url, alt, width, height);
          }
        }
      } else if (panelId === 'upload') {
        // Get values from upload form
        const fileInput = this.dialog?.querySelector('#image-file') as HTMLInputElement;
        const alt = (this.dialog?.querySelector('#image-alt-upload') as HTMLInputElement)?.value || '';
        const width = (this.dialog?.querySelector('#image-width-upload') as HTMLInputElement)?.value || '';
        const height = (this.dialog?.querySelector('#image-height-upload') as HTMLInputElement)?.value || '';
        
        if (fileInput?.files?.length) {
          const file = fileInput.files[0];
          this.handleImageUpload(file, alt, width, height, existingImage);
        }
      }
      
      this.closeImageDialog();
    });
    
    if (removeButton && existingImage) {
      removeButton.addEventListener('click', () => {
        this.removeImage(existingImage);
        this.closeImageDialog();
      });
    }
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Focus the URL input if showing URL tab
    const urlInput = this.dialog.querySelector('#image-url') as HTMLInputElement;
    if (urlInput) {
      urlInput.focus();
    }
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeImageDialog();
    }
  };
  
  /**
   * Close the image dialog
   */
  private closeImageDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog and backdrop
    const backdrop = document.querySelector('.feather-image-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Insert a new image
   * @param src The image source URL
   * @param alt The alt text
   * @param width The image width
   * @param height The image height
   */
  private insertImage(
    src: string,
    alt: string = '',
    width: string = '',
    height: string = ''
  ): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    
    // Create image wrapper
    const wrapper = document.createElement('div');
    wrapper.className = 'group inline-block relative my-2 max-w-full'; // Tailwind for wrapper
    
    // Create the image element
    const img = document.createElement('img');
    img.className = 'max-w-full h-auto block'; // Tailwind for img
    img.src = src;
    img.alt = alt;
    
    // Set dimensions if provided
    if (width) img.width = parseInt(width, 10);
    if (height) img.height = parseInt(height, 10);
    
    // Add the image to the wrapper first
    wrapper.appendChild(img);

    // Create and add edit icon
    const editIcon = document.createElement('div');
    editIcon.className = 'absolute top-2 right-2 bg-white/80 dark:bg-slate-700/80 rounded-full w-7 h-7 flex items-center justify-center cursor-pointer shadow text-gray-700 dark:text-slate-200 opacity-0 group-hover:opacity-100 transition-opacity';
    editIcon.innerHTML = '🖊'; // Consider using an SVG icon for better scaling and consistency
    editIcon.setAttribute('title', 'Edit image');
    editIcon.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.openImageDialog(img);
    });
    wrapper.appendChild(editIcon);
    
    // Delete any selected content
    range.deleteContents();
    
    // Insert the image wrapper
    range.insertNode(wrapper);
    
    // Move cursor after the image
    selection.removeAllRanges();
    const newRange = document.createRange();
    newRange.setStartAfter(wrapper);
    newRange.collapse(true);
    selection.addRange(newRange);
    
    // Trigger input event for history
    const editorElement = this.editor!.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Update an existing image
   * @param imgElement The image element to update
   * @param src The new source URL
   * @param alt The new alt text
   * @param width The new width
   * @param height The new height
   */
  private updateImage(
    imgElement: HTMLImageElement,
    src: string,
    alt: string = '',
    width: string = '',
    height: string = ''
  ): void {
    if (!this.editor) return;
    
    // Update image attributes
    imgElement.src = src;
    imgElement.alt = alt;
    
    // Update dimensions
    if (width) {
      imgElement.width = parseInt(width, 10);
    } else {
      imgElement.removeAttribute('width');
    }
    
    if (height) {
      imgElement.height = parseInt(height, 10);
    } else {
      imgElement.removeAttribute('height');
    }
    
    // Trigger input event for history
    const editorElement = this.editor!.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Remove an image
   * @param imgElement The image element to remove
   */
  private removeImage(imgElement: HTMLImageElement): void {
    if (!this.editor) return;
    
    // Find the wrapper
    const wrapper = imgElement.closest('.feather-image-wrapper');
    
    // Remove the wrapper or just the image
    const elementToRemove = wrapper || imgElement;
    if (elementToRemove.parentNode) {
      elementToRemove.parentNode.removeChild(elementToRemove);
      
      // Trigger input event for history
      const editorElement = this.editor!.getElement();
      if (editorElement) {
        editorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
      }
    }
  }
  
  /**
   * Handle image file upload
   * @param file The image file to upload
   * @param alt The alt text
   * @param width The image width
   * @param height The image height
   * @param existingImage Existing image to update
   */
  private handleImageUpload(
    file: File,
    alt: string = '',
    width: string = '',
    height: string = '',
    existingImage: HTMLImageElement | null = null
  ): void {
    // Use FileReader to handle the image
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const src = e.target?.result as string;
      
      if (existingImage) {
        this.updateImage(existingImage, src, alt, width, height);
      } else {
        this.insertImage(src, alt, width, height);
      }
    };
    
    reader.readAsDataURL(file);
  }
  
  /**
   * Handle paste events to detect and insert images
   * @param e The clipboard event
   */
  private handlePaste(e: ClipboardEvent): void {
    if (!this.editor) return;
    
    // Check if there are any images in the clipboard
    const items = e.clipboardData?.items;
    if (!items) return;
    
    for (const item of Array.from(items)) {
      // Check if the item is an image
      if (item.type.startsWith('image/')) {
        // Prevent default paste behavior
        e.preventDefault();
        
        // Get the image file
        const file = item.getAsFile();
        if (file) {
          // Handle the image upload
          this.handleImageUpload(file);
        }
        
        // Only handle one image
        break;
      }
    }
  }
  
  /**
   * Handle dragover events for image drag and drop
   * @param e The drag event
   */
  private handleDragOver(e: DragEvent): void {
    // Check if the dragged item is an image
    const items = e.dataTransfer?.items;
    if (!items) return;
    
    for (const item of Array.from(items)) {
      if (item.kind === 'file' && item.type.startsWith('image/')) {
        // Allow dropping
        e.preventDefault();
        e.dataTransfer!.dropEffect = 'copy';
        break;
      }
    }
  }
  
  /**
   * Handle drop events for image drag and drop
   * @param e The drag event
   */
  private handleDrop(e: DragEvent): void {
    if (!this.editor) return;
    
    // Check if there are any image files being dropped
    const files = e.dataTransfer?.files;
    if (!files) return;
    
    for (const file of Array.from(files)) {
      if (file.type.startsWith('image/')) {
        // Prevent default drop behavior
        e.preventDefault();
        
        // Get the selection or create one at the drop position
        const selection = window.getSelection();
        if (selection && selection.rangeCount) {
          // Use existing selection
        } else if (document.caretRangeFromPoint) {
          // If we have a drop point, create a selection there
          const range = document.caretRangeFromPoint(e.clientX, e.clientY);
          if (range) {
            selection?.removeAllRanges();
            selection?.addRange(range);
          }
        }
        
        // Handle the image upload
        this.handleImageUpload(file);
        
        // Only handle one image
        break;
      }
    }
  }
  
  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeImageDialog();
    }
    
    // Remove event listeners
    if (this.editor) {
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.removeEventListener('paste', this.pasteHandler);
        editorElement.removeEventListener('dragover', this.dragOverHandler);
        editorElement.removeEventListener('drop', this.dropHandler);
      }
    }
  }
}

// Create and export the plugin instance directly
const plugin = new ImagePlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
