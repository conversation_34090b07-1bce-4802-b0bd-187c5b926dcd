/**
 * Landmarks Plugin for FeatherJS
 * Provides document region navigation and heading hierarchy visualization
 */
import type { Editor } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
export interface LandmarksConfig {
  /**
   * Whether to show a landmarks panel by default
   */
  showByDefault: boolean;
  
  /**
   * Whether to visualize ARIA landmarks
   */
  visualizeLandmarks: boolean;
  
  /**
   * Whether to visualize heading hierarchy
   */
  visualizeHeadings: boolean;
}

const DEFAULT_CONFIG: LandmarksConfig = {
  showByDefault: false,
  visualizeLandmarks: true,
  visualizeHeadings: true
};

const config: PluginConfig = {
  id: 'landmarks',
  name: 'Landmarks',
  description: 'Provides document region navigation and heading hierarchy visualization',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'landmarks',
      command: 'landmarks',
      icon: '📑',
      label: 'Landmarks',
      tooltip: 'Show document outline and landmarks',
      group: 'accessibility',
      ariaLabel: 'Toggle document outline panel',
    }
  ],
  shortcuts: []
};

class LandmarksPlugin extends BasePlugin {
  protected config: PluginConfig & LandmarksConfig;
  private panel: HTMLElement | null = null;
  private observer: MutationObserver | null = null;
  private isOpen = false;
  
  constructor() {
    super(config);
    
    this.config = { ...config, ...DEFAULT_CONFIG };
  }
  
  /**
   * Initialize the plugin
   */
  public init(editor: Editor): void {
    this.editor = editor;
    
    // Register toolbar item
    // Note: Toolbar items are now handled by ToolbarLayoutManager
    
    // Set up mutation observer
    this.setupMutationObserver();
    
    // If showByDefault is true, open the panel
    if (this.config.showByDefault) {
      this.togglePanel();
    }
  }
  
  /**
   * Handle toolbar button click or keyboard shortcut
   */
  public handleCommand(_command: string): void {
    this.togglePanel();
  }
  
  /**
   * Toggle the landmarks panel
   */
  private togglePanel(): void {
    if (!this.editor) return;
    
    if (!this.isOpen) {
      this.openPanel();
    } else {
      this.closePanel();
    }
    
    // Update toolbar button state
    this.updateToolbarState();
  }
  
  /**
   * Open the landmarks panel
   */
  private openPanel(): void {
    if (!this.editor || this.isOpen) return;
    
    // Create panel if it doesn't exist
    if (!this.panel) {
      this.createPanel();
    }
    
    // Add panel to the editor
    if (this.panel) {
      const editorElement = this.editor.getElement();
      const editorContainer = editorElement?.parentElement;
      if (editorContainer) {
        editorContainer.appendChild(this.panel);
        this.isOpen = true;
        
        // Update content
        this.updatePanelContent();
      }
    }
  }
  
  /**
   * Close the landmarks panel
   */
  private closePanel(): void {
    if (!this.panel || !this.isOpen) return;
    
    if (this.panel.parentElement) {
      this.panel.parentElement.removeChild(this.panel);
    }
    
    this.isOpen = false;
  }
  
  /**
   * Update toolbar button state
   */
  private updateToolbarState(): void {
    // Find the toolbar button by command

    const button = document.querySelector('[data-command="landmarks"]') as HTMLButtonElement;
    if (button) {
      button.classList.toggle('active', this.isOpen);
      
      // Update ARIA attributes
      button.setAttribute('aria-pressed', this.isOpen ? 'true' : 'false');
    }
  }
  
  /**
   * Create the landmarks panel
   */
  private createPanel(): void {
    this.panel = document.createElement('div');
    this.panel.className = 'absolute right-0 top-10 w-[300px] max-h-[calc(100vh-80px)] bg-white dark:bg-slate-800 border border-r-0 border-gray-300 dark:border-slate-600 rounded-l-md shadow-[-2px_0_8px_rgba(0,0,0,0.1)] z-[1000] flex flex-col font-sans text-gray-900 dark:text-slate-200';
    this.panel.setAttribute('role', 'complementary');
    this.panel.setAttribute('aria-label', 'Document outline');
    
    // Create panel header
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center px-3 py-2 border-b border-gray-200 dark:border-slate-700 bg-gray-100 dark:bg-slate-700';
    
    const title = document.createElement('h3');
    title.className = 'm-0 text-base font-semibold';
    title.textContent = 'Document Outline';
    
    const closeButton = document.createElement('button');
    closeButton.type = 'button';
    closeButton.className = 'bg-transparent border-none text-xl cursor-pointer text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200';
    closeButton.innerHTML = '&times;';
    closeButton.setAttribute('aria-label', 'Close outline panel');
    closeButton.addEventListener('click', () => this.togglePanel());
    
    header.appendChild(title);
    header.appendChild(closeButton);
    
    // Create tabs
    const tabs = document.createElement('div');
    tabs.className = 'flex border-b border-gray-200 dark:border-slate-700';
    
    const headingsTab = document.createElement('button');
    headingsTab.type = 'button';
    // Active tab by default
    headingsTab.className = 'flex-1 px-3 py-2 text-center cursor-pointer text-sm border-r border-gray-200 dark:border-slate-600 bg-white dark:bg-slate-800 font-semibold border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 -mb-px';
    headingsTab.textContent = 'Headings';
    headingsTab.setAttribute('data-tab', 'headings');
    headingsTab.setAttribute('aria-selected', 'true');
    
    const landmarksTab = document.createElement('button');
    landmarksTab.type = 'button';
    landmarksTab.className = 'flex-1 px-3 py-2 text-center bg-gray-100 dark:bg-slate-700 cursor-pointer text-sm text-gray-700 dark:text-slate-300 hover:bg-gray-200 dark:hover:bg-slate-600';
    landmarksTab.textContent = 'Landmarks';
    landmarksTab.setAttribute('data-tab', 'landmarks');
    landmarksTab.setAttribute('aria-selected', 'false');
    
    tabs.appendChild(headingsTab);
    tabs.appendChild(landmarksTab);
    
    // Add tab click handlers
    headingsTab.addEventListener('click', () => this.switchTab('headings'));
    landmarksTab.addEventListener('click', () => this.switchTab('landmarks'));
    
    // Create content container
    const content = document.createElement('div');
    content.className = 'flex-1 overflow-y-auto p-3';
    
    // Create headings panel
    const headingsPanel = document.createElement('div');
    headingsPanel.className = 'block'; // Active by default
    headingsPanel.setAttribute('data-panel', 'headings');
    
    // Create landmarks panel
    const landmarksPanel = document.createElement('div');
    landmarksPanel.className = 'hidden'; // Hidden by default
    landmarksPanel.setAttribute('data-panel', 'landmarks');
    
    content.appendChild(headingsPanel);
    content.appendChild(landmarksPanel);
    
    // Create toolbar
    const toolbar = document.createElement('div');
    toolbar.className = 'flex p-3 border-t border-gray-200 dark:border-slate-700 justify-center';
    
    const visualizeButton = document.createElement('button');
    visualizeButton.type = 'button';
    visualizeButton.className = 'bg-gray-100 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded py-1.5 px-3 text-[13px] cursor-pointer hover:bg-gray-200 dark:hover:bg-slate-600';
    visualizeButton.textContent = 'Visualize Structure';
    visualizeButton.addEventListener('click', () => this.toggleVisualization());
    
    toolbar.appendChild(visualizeButton);
    
    // Assemble the panel
    this.panel.appendChild(header);
    this.panel.appendChild(tabs);
    this.panel.appendChild(content);
    this.panel.appendChild(toolbar);
  }
  
  /**
   * Switch between tabs in the panel
   */
  private switchTab(tabName: 'headings' | 'landmarks'): void {
    if (!this.panel) return;
    
    const activeTabClasses = ['bg-white', 'dark:bg-slate-800', 'font-semibold', 'border-b-2', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400', '-mb-px'];
    const inactiveTabClasses = ['bg-gray-100', 'dark:bg-slate-700', 'text-gray-700', 'dark:text-slate-300', 'hover:bg-gray-200', 'dark:hover:bg-slate-600'];

    // Update tab buttons
    const tabs = this.panel.querySelectorAll<HTMLButtonElement>('[data-tab]');
    tabs.forEach(tab => {
      const isActive = tab.getAttribute('data-tab') === tabName;
      if (isActive) {
        tab.classList.add(...activeTabClasses);
        tab.classList.remove(...inactiveTabClasses);
      } else {
        tab.classList.add(...inactiveTabClasses);
        tab.classList.remove(...activeTabClasses);
      }
      tab.setAttribute('aria-selected', isActive ? 'true' : 'false');
    });
    
    // Update content panels
    const panels = this.panel.querySelectorAll<HTMLDivElement>('[data-panel]');
    panels.forEach(panel => {
      const isActive = panel.getAttribute('data-panel') === tabName;
      panel.classList.toggle('block', isActive);
      panel.classList.toggle('hidden', !isActive);
    });
    
    // Update content based on active tab
    this.updatePanelContent(tabName);
  }
  
  /**
   * Toggle visualization of document structure
   */
  private toggleVisualization(): void {
    if (!this.editor) return;
    
    // Toggle config
    this.config.visualizeLandmarks = !this.config.visualizeLandmarks;
    this.config.visualizeHeadings = !this.config.visualizeHeadings;
    
    // Update visualization
    if (this.config.visualizeLandmarks || this.config.visualizeHeadings) {
      this.visualizeDocumentStructure();
    } else {
      this.removeVisualization();
    }
  }
  
  /**
   * Set up mutation observer to watch for document changes
   */
  private setupMutationObserver(): void {
    if (!this.editor) return;
    
    this.observer = new MutationObserver(() => {
      if (this.isOpen) {
        this.updatePanelContent();
      }
      
      if (this.config.visualizeLandmarks || this.config.visualizeHeadings) {
        this.visualizeDocumentStructure();
      }
    });
    
    // Start observing
    const editorElement = this.editor.getElement();
    if (editorElement) {
      this.observer.observe(editorElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['role', 'aria-label']
      });
    }
  }
  
  /**
   * Update panel content based on the active tab
   */
  private updatePanelContent(activeTab?: 'headings' | 'landmarks'): void {
    if (!this.panel || !this.editor) return;
    
    // Determine which tab is active if not specified
    if (!activeTab) {
      const activeTabEl = this.panel.querySelector('.feather-landmarks-tab.active');
      activeTab = (activeTabEl?.getAttribute('data-tab') as 'headings' | 'landmarks') || 'headings';
    }
    
    // Update content based on active tab
    if (activeTab === 'headings') {
      this.updateHeadingsPanel();
    } else {
      this.updateLandmarksPanel();
    }
  }
  
  /**
   * Update headings panel with document outline
   */
  private updateHeadingsPanel(): void {
    if (!this.panel || !this.editor) return;
    
    const headingsPanel = this.panel.querySelector('[data-panel="headings"]');
    if (!headingsPanel) return;
    
    // Find all headings in the editor
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    const headings = Array.from(editorElement.querySelectorAll('h1, h2, h3, h4, h5, h6'));
    
    // Clear existing content
    headingsPanel.innerHTML = '';
    
    if (headings.length === 0) {
      const message = document.createElement('p');
      message.className = 'text-sm text-gray-500 dark:text-slate-400 p-3';
      message.textContent = 'No headings found in the document.';
      headingsPanel.appendChild(message);
      return;
    }
    
    // Create hierarchy tree
    const tree = document.createElement('ul');
    tree.className = 'list-none p-0 m-0'; // Tailwind for tree
    
    // Track the current level for proper nesting
    let currentLevel = 0;
    let currentParent = tree;
    const parentStack: HTMLElement[] = [tree];
    
    headings.forEach(heading => {
      // Get heading level (1-6)
      const level = parseInt(heading.tagName.charAt(1));
      
      // Create list item
      const item = document.createElement('li');
      const itemBaseClasses = 'mb-2 flex items-center'; // Changed to const
      const indentClasses: {[key: number]: string} = {1: 'ml-0', 2: 'ml-4', 3: 'ml-8', 4: 'ml-12', 5: 'ml-16', 6: 'ml-20'};
      item.className = `${itemBaseClasses} ${indentClasses[level] || 'ml-0'}`;
      
      // Create link
      const link = document.createElement('a');
      let linkClasses = 'flex-1 no-underline text-gray-700 dark:text-slate-200 px-2 py-1 rounded text-sm block overflow-hidden text-ellipsis whitespace-nowrap hover:bg-gray-100 dark:hover:bg-slate-700';
      if (level === 1) linkClasses += ' font-semibold text-base';
      else if (level === 2) linkClasses += ' font-medium';
      else if (level >= 3) linkClasses += ' text-xs'; // Smaller for deeper levels
      link.className = linkClasses;
      link.textContent = heading.textContent || 'Untitled heading';
      link.setAttribute('href', '#');
      link.addEventListener('click', (e) => {
        e.preventDefault();
        this.scrollToElement(heading as HTMLElement);
      });
      
      item.appendChild(link);
      
      // Handle nesting based on heading level
      if (level > currentLevel) {
        // Going deeper in the hierarchy
        const nestedList = document.createElement('ul');
        nestedList.className = 'list-none p-0 m-0 pl-4'; // Tailwind for nested list with indent
        currentParent.lastElementChild?.appendChild(nestedList); // This might need adjustment if item structure changes
        parentStack.push(nestedList);
        currentParent = nestedList;
      } else if (level < currentLevel) {
        // Going up in the hierarchy
        for (let i = 0; i < (currentLevel - level); i++) {
          parentStack.pop();
        }
        currentParent = parentStack[parentStack.length - 1] as HTMLUListElement;
      }
      
      // Add item to current parent
      currentParent.appendChild(item);
      
      // Update current level
      currentLevel = level;
    });
    
    headingsPanel.appendChild(tree);
  }
  
  /**
   * Update landmarks panel with document regions
   */
  private updateLandmarksPanel(): void {
    if (!this.panel || !this.editor) return;
    
    const landmarksPanel = this.panel.querySelector('[data-panel="landmarks"]');
    if (!landmarksPanel) return;
    
    // Clear existing content
    landmarksPanel.innerHTML = '';
    
    // Find all elements with ARIA landmark roles
    const landmarkRoles = [
      'banner', 'complementary', 'contentinfo', 'form', 'main', 
      'navigation', 'region', 'search', 'application'
    ];
    
    // Also include semantic HTML5 elements that implicitly define landmarks
    const implicitLandmarks = [
      'header', 'footer', 'main', 'nav', 'aside', 'section', 'article'
    ];
    
    // Create selectors for both explicit and implicit landmarks
    const explicitSelector = landmarkRoles.map(role => `[role="${role}"]`).join(', ');
    const implicitSelector = implicitLandmarks.join(', ');
    
    // Combine selectors and find all landmarks
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    const landmarks = Array.from(editorElement.querySelectorAll(`${explicitSelector}, ${implicitSelector}`));
    
    // No landmarks found
    if (landmarks.length === 0) {
      const message = document.createElement('p');
      message.className = 'text-sm text-gray-500 dark:text-slate-400 p-3';
      message.textContent = 'No landmarks or regions found in the document.';
      landmarksPanel.appendChild(message);
      return;
    }
    
    // Create list for landmarks
    const list = document.createElement('ul');
    list.className = 'list-none p-0 m-0'; // Tailwind for list
    
    landmarks.forEach(landmark => {
      // Create list item
      const item = document.createElement('li');
      item.className = 'mb-2 flex items-center'; // Tailwind for item
      
      // Determine landmark type and name
      let type = '';
      
      // Check for explicit role
      const role = landmark.getAttribute('role');
      if (role && landmarkRoles.includes(role)) {
        type = role;
      } else {
        // Check for implicit role from HTML tag
        type = landmark.tagName.toLowerCase();
      }
      
      // Get landmark name (from aria-label, aria-labelledby, or tag name)
      let name = landmark.getAttribute('aria-label') || '';
      
      // If no aria-label, check aria-labelledby
      const labelledBy = landmark.getAttribute('aria-labelledby');
      if (!name && labelledBy) {
        const labelEl = document.getElementById(labelledBy);
        if (labelEl) name = labelEl.textContent || '';
      }
      
      // If still no name, use type with first letter capitalized
      if (!name) {
        name = type.charAt(0).toUpperCase() + type.slice(1);
      }
      
      // Create link
      const link = document.createElement('a');
      link.className = 'flex-1 no-underline text-gray-700 dark:text-slate-200 px-2 py-1 rounded text-sm block overflow-hidden text-ellipsis whitespace-nowrap hover:bg-gray-100 dark:hover:bg-slate-700';
      link.textContent = name;
      link.setAttribute('href', '#');
      link.addEventListener('click', (e) => {
        e.preventDefault();
        if (landmark instanceof HTMLElement) {
          this.scrollToElement(landmark);
        }
      });
      
      // Add badge with landmark type
      const badge = document.createElement('span');
      badge.className = 'text-[11px] bg-gray-200 dark:bg-slate-600 text-gray-500 dark:text-slate-300 px-1.5 py-0.5 rounded-full ml-2 whitespace-nowrap';
      badge.textContent = type;
      
      item.appendChild(link);
      item.appendChild(badge);
      list.appendChild(item);
    });
    
    landmarksPanel.appendChild(list);
  }
  
  /**
   * Scroll to an element in the editor
   */
  private scrollToElement(element: HTMLElement): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    
    // Highlight the element
    this.highlightElement(element);
    
    // Scroll to the element
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Focus the element
    element.setAttribute('tabindex', '-1');
    element.focus();
    
    // Remove tabindex after focusing
    setTimeout(() => {
      element.removeAttribute('tabindex');
    }, 1000);
  }
  
  /**
   * Highlight an element temporarily
   */
  private highlightElement(element: HTMLElement): void {
    const highlightClasses = ['js-landmark-highlight', 'outline', 'outline-3', 'outline-blue-500', '!outline-offset-2', 'transition-all', 'duration-300', 'ease-in-out'];
    // Add highlight class
    element.classList.add(...highlightClasses);
    
    // Remove highlight after a delay
    setTimeout(() => {
      element.classList.remove(...highlightClasses);
    }, 3000);
  }
  
  /**
   * Visualize document structure with outlines and labels
   */
  private visualizeDocumentStructure(): void {
    if (!this.editor) return;
    
    // Remove existing visualization first
    this.removeVisualization();
    
    // Visualize headings if enabled
    const baseVisualizeClasses = ['js-landmark-visualize', 'relative', '!outline', '!outline-2', '!outline-dashed', '!outline-gray-400', 'dark:!outline-slate-500', '!outline-offset-2', '!my-1'];
    const labelClasses = 'absolute top-0 left-0 text-[10px] bg-gray-700 dark:bg-slate-600 text-white px-1 py-0.5 rounded-br-md font-mono z-[1]'; // Corrected: removed trailing double quote
    
    if (this.config.visualizeHeadings) {
      const editorElement = this.editor.getElement();
      if (!editorElement) return;
      const headings = editorElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach(heading => {
        heading.classList.add(...baseVisualizeClasses);
        const levelNum = heading.tagName.charAt(1);
        let colorClass = '';
        if (levelNum === '1') colorClass = '!outline-red-500';
        else if (levelNum === '2') colorClass = '!outline-orange-500';
        else if (levelNum === '3') colorClass = '!outline-yellow-500';
        else colorClass = '!outline-purple-500'; // H4-H6
        heading.classList.add(colorClass);
        
        if (!heading.querySelector('.js-landmark-label')) {
          const levelIndicator = document.createElement('span');
          levelIndicator.className = `${labelClasses} js-landmark-label`;
          levelIndicator.textContent = `H${levelNum}`;
          levelIndicator.setAttribute('aria-hidden', 'true');
          heading.insertBefore(levelIndicator, heading.firstChild);
        }
      });
    }
    
    if (this.config.visualizeLandmarks) {
      const landmarkRoles = ['banner', 'complementary', 'contentinfo', 'form', 'main', 'navigation', 'region', 'search', 'application'];
      const implicitLandmarks = ['header', 'footer', 'main', 'nav', 'aside', 'section', 'article'];
      const explicitSelector = landmarkRoles.map(role => `[role="${role}"]`).join(', ');
      const implicitSelector = implicitLandmarks.join(', ');
      
      const editorElement = this.editor.getElement();
      if (!editorElement) return;
      const landmarks = editorElement.querySelectorAll(`${explicitSelector}, ${implicitSelector}`);
      
      landmarks.forEach(landmark => {
        landmark.classList.add(...baseVisualizeClasses, '!outline-green-500');
        
        const type = landmark.getAttribute('role') || landmark.tagName.toLowerCase();
        if (type && !landmark.querySelector('.js-landmark-label')) {
          const typeEl = document.createElement('span');
          typeEl.className = `${labelClasses} js-landmark-label`;
          typeEl.textContent = type;
          typeEl.setAttribute('aria-hidden', 'true');
          landmark.insertBefore(typeEl, landmark.firstChild);
        }
      });
    }
  }
  
  /**
   * Remove visualization
   */
  private removeVisualization(): void {
    if (!this.editor) return;
    
    // Remove visualization classes
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    const visualizedElements = editorElement.querySelectorAll<HTMLElement>('.js-landmark-visualize');
    visualizedElements.forEach(element => {
      // Remove all Tailwind classes related to visualization
      // This is a bit broad but ensures cleanup. Specific classes could be stored and removed.
      element.className = Array.from(element.classList).filter(cls => 
        !cls.startsWith('!outline-') && 
        !cls.includes('js-landmark-visualize') &&
        !['relative', '!outline', '!outline-2', '!outline-dashed', '!outline-offset-2', '!my-1'].includes(cls)
      ).join(' ');
      if (element.className.trim() === '') element.removeAttribute('class');
    });
    
    const indicators = editorElement.querySelectorAll('.js-landmark-label');
    indicators.forEach(indicator => indicator.remove());
  }
  
  /**
   * Clean up on destroy
   */
  public destroy(): void {
    // Disconnect observer
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    // Close panel
    this.closePanel();
    
    // Remove visualization
    this.removeVisualization();
    
    // Call parent destroy
    super.destroy();
  }
}

// Create and export the plugin instance directly
const plugin = new LandmarksPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
