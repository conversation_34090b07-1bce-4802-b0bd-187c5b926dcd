/**
 * FeatherJS Accessibility Plugins
 *
 * This module exports accessibility-focused plugins for the FeatherJS editor:
 * - a11y-checker: Scans content for accessibility issues
 * - alt-text-inspector: Enforces alt text on images
 * - landmarks: Visualizes document structure and ARIA landmarks
 * - placeholder: Manages accessible form field placeholders and hints
 */

import type { Plugin } from '../../types';
import A11yCheckerPlugin from './a11y-checker';
import AltTextInspectorPlugin from './alt-text-inspector';
import LandmarksPlugin from './landmarks';
import PlaceholderPlugin from './placeholder';

// Export individual plugins
export {
  A11yCheckerPlugin,
  AltTextInspectorPlugin,
  LandmarksPlugin,
  PlaceholderPlugin
};

// Export a combined plugin group for convenience
export const AccessibilityPlugins: Plugin[] = [
  A11yCheckerPlugin,
  AltTextInspectorPlugin,
  LandmarksPlugin,
  PlaceholderPlugin
];

// Export the accessibility plugins array directly
// PluginManager will handle individual plugin registration
export default AccessibilityPlugins;