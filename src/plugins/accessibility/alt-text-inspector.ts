/**
 * Alt Text Inspector Plugin for FeatherJS
 * Monitors for images without alt text and prompts users to add it
 */

import { BasePlugin, PluginConfig } from '../base-plugin';
import type { Editor } from '../../types';

export interface AltTextInspectorConfig {
  /**
   * Whether to automatically prompt for alt text when an image is inserted
   */
  autoPrompt: boolean;
  
  /**
   * Whether to highlight images without alt text
   */
  highlightMissingAlt: boolean;
  
  /**
   * Whether to allow empty alt text for decorative images
   */
  allowEmptyAltForDecorative: boolean;
}

const DEFAULT_CONFIG: AltTextInspectorConfig = {
  autoPrompt: true,
  highlightMissingAlt: true,
  allowEmptyAltForDecorative: true
};

/**
 * Alt Text Inspector Plugin configuration
 */
const config: PluginConfig = {
  id: 'alt-text-inspector',
  name: 'Alt Text Inspector',
  description: 'Monitor and prompt for image alt text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'alt-text-inspector',
      command: 'alt-text-inspector',
      icon: '🖼✓',
      label: 'Alt Text Inspector',
      tooltip: 'Monitor and prompt for image alt text',
      group: 'accessibility',
      ariaLabel: 'Toggle alt text inspector',
    }
  ],
  shortcuts: []
};

class AltTextInspectorPlugin extends BasePlugin {
  private observer: MutationObserver | null = null;
  private processingImage = false;
  protected config: PluginConfig & AltTextInspectorConfig;
  
  constructor() {
    super(config);
    this.config = { ...config, ...DEFAULT_CONFIG };
  }
  
  /**
   * Initialize the plugin
   */
  public init(editor: Editor): void {
    this.editor = editor;
    
    // Register toolbar item
    // Note: Toolbar items are now handled by ToolbarLayoutManager
    
    // Set up MutationObserver to watch for new images
    this.setupMutationObserver();
    
    // Scan existing images
    if (this.config.highlightMissingAlt) {
      this.highlightImagesWithoutAlt();
    }
  }
  
  /**
   * Handle toolbar button click or keyboard shortcut
   */
  public handleCommand(_command: string): void {
    // Toggle inspector active state
    this.toggleInspector();
  }
  
  /**
   * Toggle the inspector on/off
   */
  private toggleInspector(): void {
    if (!this.editor) return;
    
    // Toggle config
    this.config.highlightMissingAlt = !this.config.highlightMissingAlt;
    this.config.autoPrompt = !this.config.autoPrompt;
    
    // Update toolbar button state
    this.updateToolbarState();
    
    // Apply or remove highlighting based on new state
    if (this.config.highlightMissingAlt) {
      this.highlightImagesWithoutAlt();
    } else {
      this.removeAllHighlights();
    }
    
    // Show feedback message
    this.showFeedbackMessage(
      this.config.highlightMissingAlt ? 
        'Alt Text Inspector activated' : 
        'Alt Text Inspector deactivated'
    );
  }
  
  /**
   * Update toolbar button state
   */
  private updateToolbarState(): void {
    // Find the toolbar button by command

    const button = document.querySelector('[data-command="alt-text-inspector"]') as HTMLButtonElement;
    if (button) {
      button.classList.toggle('active', this.config.highlightMissingAlt);
      
      // Update ARIA attributes
      button.setAttribute('aria-pressed', this.config.highlightMissingAlt ? 'true' : 'false');
    }
  }
  
  /**
   * Set up mutation observer to watch for new images
   */
  private setupMutationObserver(): void {
    if (!this.editor) return;
    
    this.observer = new MutationObserver((mutations) => {
      // Check for added nodes
      let shouldCheckImages = false;
      
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length) {
          shouldCheckImages = true;
          break;
        }
      }
      
      if (shouldCheckImages) {
        this.handleNewContentAdded();
      }
    });
    
    // Start observing
    const editorElement = this.editor.getElement();
    if (editorElement) {
      this.observer.observe(editorElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['alt', 'src']
      });
    }
  }
  
  /**
   * Handle new content being added to the editor
   */
  private handleNewContentAdded(): void {
    if (!this.editor || this.processingImage) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Check for images without alt text
    const images = editorElement.querySelectorAll('img');
    
    // Process newly added images without alt text
    const imagesWithoutAlt = Array.from(images).filter(img => {
      // Check if image has no alt attribute or empty alt when not allowed
      const hasNoAlt = !img.hasAttribute('alt');
      const hasEmptyAlt = img.alt === '' && !this.config.allowEmptyAltForDecorative;
      return hasNoAlt || hasEmptyAlt;
    });
    
    if (imagesWithoutAlt.length > 0 && this.config.highlightMissingAlt) {
      // Highlight images without alt
      imagesWithoutAlt.forEach(img => this.highlightImage(img));
      
      // If auto-prompt is enabled, prompt for alt text for the first image
      if (this.config.autoPrompt && !this.processingImage) {
        this.processingImage = true;
        setTimeout(() => {
          this.promptForAltText(imagesWithoutAlt[0]);
          this.processingImage = false;
        }, 500); // Small delay to avoid interrupting user typing
      }
    }
  }
  
  /**
   * Highlight all images without alt text
   */
  private highlightImagesWithoutAlt(): void {
    if (!this.editor || !this.config.highlightMissingAlt) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Remove existing highlights first
    this.removeAllHighlights();
    
    // Find and highlight images without alt text
    const images = editorElement.querySelectorAll('img');
    images.forEach(img => {
      const hasNoAlt = !img.hasAttribute('alt');
      const hasEmptyAlt = img.alt === '' && !this.config.allowEmptyAltForDecorative;
      
      if (hasNoAlt || hasEmptyAlt) {
        this.highlightImage(img);
      }
    });
  }
  
  /**
   * Highlight a specific image
   */
  private highlightImage(img: HTMLImageElement): void {
    // Add highlighting class
    img.classList.add('js-alt-text-highlight', 'outline', 'outline-2', 'outline-orange-500', 'outline-offset-2', 'ring-4', 'ring-orange-500/30', 'cursor-pointer');
    
    // Add data attribute for styling
    img.setAttribute('data-feather-alt-status', 'missing');
    
    // Add click handler to prompt for alt text
    img.addEventListener('click', this.handleImageClick);
  }
  
  /**
   * Remove all highlights
   */
  private removeAllHighlights(): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    const highlightedImages = editorElement.querySelectorAll('.js-alt-text-highlight');
    highlightedImages.forEach(img => {
      img.classList.remove('js-alt-text-highlight', 'outline', 'outline-2', 'outline-orange-500', 'outline-offset-2', 'ring-4', 'ring-orange-500/30', 'cursor-pointer');
      img.removeAttribute('data-feather-alt-status');
      img.removeEventListener('click', this.handleImageClick);
    });
  }
  
  /**
   * Handle click on an image
   */
  private handleImageClick = (event: Event): void => {
    const img = event.target as HTMLImageElement;
    if (this.config.autoPrompt) {
      this.promptForAltText(img);
      event.preventDefault();
      event.stopPropagation();
    }
  };
  
  /**
   * Prompt user for alt text for an image
   */
  private promptForAltText(img: HTMLImageElement): void {
    if (!this.editor) return;
    
    // Create dialog overlay
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-black/50 flex justify-center items-center z-[10000]';
    document.body.appendChild(backdrop);
    
    // Create dialog
    const dialog = document.createElement('div');
    dialog.className = 'bg-white dark:bg-slate-800 rounded-lg shadow-lg w-[500px] max-w-[90%] max-h-[90vh] overflow-y-auto font-sans text-gray-900 dark:text-slate-200 flex flex-col';
    dialog.setAttribute('role', 'dialog');
    dialog.setAttribute('aria-labelledby', 'feather-alt-text-title');
    dialog.setAttribute('aria-modal', 'true');
    
    // Header
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center px-4 py-3 border-b border-gray-200 dark:border-slate-700';
    const title = document.createElement('h3');
    title.id = 'feather-alt-text-title';
    title.className = 'm-0 text-lg font-semibold';
    title.textContent = 'Add Alt Text';
    const closeButtonDialog = document.createElement('button');
    closeButtonDialog.type = 'button';
    closeButtonDialog.className = 'bg-transparent border-none text-2xl cursor-pointer text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200';
    closeButtonDialog.setAttribute('aria-label', 'Close dialog');
    closeButtonDialog.innerHTML = '&times;';
    header.appendChild(title);
    header.appendChild(closeButtonDialog);
    dialog.appendChild(header);

    // Body
    const body = document.createElement('div');
    body.className = 'p-4 flex flex-col gap-4';
    
    const previewDiv = document.createElement('div');
    previewDiv.className = 'flex justify-center border border-gray-200 dark:border-slate-700 rounded p-2 bg-gray-100 dark:bg-slate-700';
    const previewImg = document.createElement('img');
    previewImg.src = img.src;
    previewImg.alt = ""; // Decorative for preview
    previewImg.style.maxWidth = '200px';
    previewImg.style.maxHeight = '150px';
    previewDiv.appendChild(previewImg);
    body.appendChild(previewDiv);

    const formDiv = document.createElement('div');
    formDiv.className = 'flex flex-col gap-3';
    const formP = document.createElement('p');
    formP.className = 'text-sm text-gray-700 dark:text-slate-300';
    formP.textContent = "Add alternative text to describe this image for users who can't see it. Good alt text should be concise but descriptive.";
    formDiv.appendChild(formP);

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'flex flex-col gap-1';
    const labelAlt = document.createElement('label');
    labelAlt.htmlFor = 'feather-alt-text-input';
    labelAlt.className = 'text-sm font-medium text-gray-700 dark:text-slate-300';
    labelAlt.textContent = 'Alt Text:';
    const inputAlt = document.createElement('input');
    inputAlt.type = 'text';
    inputAlt.id = 'feather-alt-text-input';
    inputAlt.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded text-sm bg-white dark:bg-slate-700 focus:ring-blue-500 focus:border-blue-500';
    inputAlt.placeholder = 'Describe this image...';
    fieldDiv.appendChild(labelAlt);
    fieldDiv.appendChild(inputAlt);
    formDiv.appendChild(fieldDiv);

    const decorativeDiv = document.createElement('div');
    decorativeDiv.className = 'flex flex-col gap-1';
    const labelDecorative = document.createElement('label');
    labelDecorative.className = 'flex items-center text-sm text-gray-700 dark:text-slate-300 cursor-pointer';
    const checkboxDecorative = document.createElement('input');
    checkboxDecorative.type = 'checkbox';
    checkboxDecorative.id = 'feather-decorative-checkbox';
    checkboxDecorative.className = 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2 cursor-pointer';
    labelDecorative.appendChild(checkboxDecorative);
    labelDecorative.appendChild(document.createTextNode(' This is a decorative image (empty alt text)'));
    const smallDecorative = document.createElement('small');
    smallDecorative.className = 'text-gray-500 dark:text-slate-400 text-xs ml-6'; // Indent to align with checkbox text
    smallDecorative.textContent = "Use for images that are purely decorative and don't convey content information.";
    decorativeDiv.appendChild(labelDecorative);
    decorativeDiv.appendChild(smallDecorative);
    formDiv.appendChild(decorativeDiv);
    body.appendChild(formDiv);
    dialog.appendChild(body);

    // Footer
    const footer = document.createElement('div');
    footer.className = 'flex justify-end gap-2 px-4 py-3 border-t border-gray-200 dark:border-slate-700';
    const cancelButtonFooter = document.createElement('button');
    cancelButtonFooter.type = 'button';
    cancelButtonFooter.className = 'py-2 px-4 rounded text-sm cursor-pointer font-medium bg-transparent border border-gray-300 dark:border-slate-600 text-gray-700 dark:text-slate-200 hover:bg-gray-100 dark:hover:bg-slate-700';
    cancelButtonFooter.textContent = 'Cancel';
    const saveButtonFooter = document.createElement('button');
    saveButtonFooter.type = 'button';
    saveButtonFooter.className = 'py-2 px-4 rounded text-sm cursor-pointer font-medium bg-blue-500 hover:bg-blue-600 text-white border-none';
    saveButtonFooter.textContent = 'Save Alt Text';
    footer.appendChild(cancelButtonFooter);
    footer.appendChild(saveButtonFooter);
    dialog.appendChild(footer);
    
    // Append dialog to backdrop so it's centered
    backdrop.appendChild(dialog); 
    // document.body.appendChild(dialog); // Original was direct to body
    
    // Focus the input field
    setTimeout(() => {
      const input = document.getElementById('feather-alt-text-input') as HTMLInputElement;
      if (input) {
        input.focus();
        
        // If image already has alt text, pre-fill it
        if (img.hasAttribute('alt')) {
          input.value = img.alt;
          
          // If empty alt, check the decorative checkbox
          if (img.alt === '') {
            const checkbox = document.getElementById('feather-decorative-checkbox') as HTMLInputElement;
            if (checkbox) checkbox.checked = true;
          }
        }
      }
    }, 100);
    
    // Handle close button
    const closeButton = dialog.querySelector('.feather-alt-text-close');
    closeButton?.addEventListener('click', () => {
      this.closeAltTextDialog(backdrop, dialog);
    });
    
    // Handle cancel button
    const cancelButton = dialog.querySelector('.feather-alt-text-cancel');
    cancelButton?.addEventListener('click', () => {
      this.closeAltTextDialog(backdrop, dialog);
    });
    
    // Handle save button
    const saveButton = dialog.querySelector('.feather-alt-text-save');
    saveButton?.addEventListener('click', () => {
      this.saveAltText(img, backdrop, dialog);
    });
    
    // Handle decorative checkbox
    const decorativeCheckbox = document.getElementById('feather-decorative-checkbox') as HTMLInputElement;
    const altTextInput = document.getElementById('feather-alt-text-input') as HTMLInputElement;
    
    if (decorativeCheckbox && altTextInput) {
      decorativeCheckbox.addEventListener('change', () => {
        if (decorativeCheckbox.checked) {
          altTextInput.value = '';
          altTextInput.disabled = true;
        } else {
          altTextInput.disabled = false;
        }
      });
    }
    
    // Handle Enter key in input
    altTextInput?.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        this.saveAltText(img, backdrop, dialog);
      }
    });
    
    // Handle Escape key
    document.addEventListener('keydown', this.handleKeyDown);
  }
  
  /**
   * Handle keydown events for dialog
   */
  private handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      const backdrop = document.querySelector('.feather-alt-text-backdrop');
      const dialog = document.querySelector('.feather-alt-text-dialog');
      
      if (backdrop && dialog) {
        this.closeAltTextDialog(backdrop as HTMLElement, dialog as HTMLElement);
      }
    }
  };
  
  /**
   * Close the alt text dialog
   */
  private closeAltTextDialog(backdrop: HTMLElement, dialog: HTMLElement): void {
    document.body.removeChild(backdrop);
    document.body.removeChild(dialog);
    document.removeEventListener('keydown', this.handleKeyDown);
  }
  
  /**
   * Save alt text for an image
   */
  private saveAltText(img: HTMLImageElement, backdrop: HTMLElement, dialog: HTMLElement): void {
    const altTextInput = document.getElementById('feather-alt-text-input') as HTMLInputElement;
    const decorativeCheckbox = document.getElementById('feather-decorative-checkbox') as HTMLInputElement;
    
    // Get alt text value (empty string if decorative)
    let altText = '';
    
    if (decorativeCheckbox && decorativeCheckbox.checked) {
      // Empty alt text for decorative images
      altText = '';
    } else if (altTextInput) {
      altText = altTextInput.value.trim();
    }
    
    // Apply alt text to the image
    img.setAttribute('alt', altText);
    
    // Remove highlighting
    img.classList.remove('feather-missing-alt');
    img.removeAttribute('data-feather-alt-status');
    img.removeEventListener('click', this.handleImageClick);
    
    // Close dialog
    this.closeAltTextDialog(backdrop, dialog);
    
    // Trigger content change event for history tracking
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(new InputEvent('input', { bubbles: true, cancelable: true }));
    }
    
    // Show feedback
    this.showFeedbackMessage('Alt text updated successfully');
  }
  
  /**
   * Show feedback message
   */
  private showFeedbackMessage(message: string): void {
    if (!this.editor) return;
    
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = 'fixed bottom-5 right-5 bg-gray-800 dark:bg-slate-700 text-white py-2 px-4 rounded font-sans z-[9999] animate-fade-in-out';
    feedback.textContent = message;
    feedback.setAttribute('role', 'status');
    feedback.setAttribute('aria-live', 'polite');
    
    // Add to the document
    document.body.appendChild(feedback);
    
    // Remove after a delay
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback);
      }
    }, 3000);
  }
  
  /**
   * Clean up on destroy
   */
  public destroy(): void {
    // Remove MutationObserver
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    // Remove highlights
    this.removeAllHighlights();
    
    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown);
    
    // Call parent destroy
    super.destroy();
  }
}

// Create and export the plugin instance directly
const plugin = new AltTextInspectorPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
