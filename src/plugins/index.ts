/**
 * FeatherJS Plugins
 *
 * This is the main entry point for all FeatherJS plugins.
 * It exports individual plugins as well as grouped plugin collections
 * for easier integration.
 */

// Import plugin groups
import { InlineFormattingPlugins } from './inline-formatting';
import { StructurePlugins } from './structure';
import { MediaPlugins } from './media';
import { UtilityPlugins } from './utilities';
import { AccessibilityPlugins } from './accessibility';
import { CollaborationPlugins } from './collaboration';

// Import plugin interface and factory
import type { Plugin, Editor } from '../types';

// Export all plugin groups
export {
  InlineFormattingPlugins,
  StructurePlugins,
  MediaPlugins,
  UtilityPlugins,
  AccessibilityPlugins,
  CollaborationPlugins
};

// Export individual plugin groups
export * from './inline-formatting';
export * from './structure';
export * from './media';
export * from './utilities';
export * from './accessibility';
export * from './collaboration';

/**
 * All plugins bundled as a single plugin group
 * This allows registering all plugins with a single call
 */
class AllPluginsGroup {
  // Flattened array of all individual plugins
  public plugins: Plugin[] = [
    ...InlineFormattingPlugins,
    ...StructurePlugins,
    ...MediaPlugins,
    ...UtilityPlugins,
    ...AccessibilityPlugins,
    ...CollaborationPlugins
  ];

  public init(editor: Editor): void {
    // Initialize each individual plugin
    this.plugins.forEach(plugin => plugin.init(editor));
  }

  public destroy(): void {
    this.plugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
}

// Export the complete plugin bundle
export const allPlugins = new AllPluginsGroup();
