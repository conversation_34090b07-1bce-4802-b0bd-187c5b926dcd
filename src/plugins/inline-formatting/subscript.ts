import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Subscript formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'subscript',
  name: 'Subscript',
  description: 'Format selected text as subscript',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'subscript',
      command: 'subscript',
      icon: 'x₂',
      label: 'Subscript',
      tooltip: 'Subscript (Ctrl/⌘+,)',
      group: 'formatting',
      ariaLabel: 'Subscript formatting',
    }
  ],
  shortcuts: [
    {
      command: 'subscript',
      key: ',',
      ctrlKey: true,
      description: 'Apply subscript formatting'
    }
  ]
};

/**
 * Subscript formatting plugin implementation
 * Uses <sub> element for semantic HTML
 */
export class SubscriptPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the subscript command
   * @param command The command to handle
   */
  public handleCommand(command: string): void {
    if (command === 'subscript' && this.editor) {
      this.editor.format('subscript');
    }
  }
}

// Create and export the plugin instance directly
const plugin = new SubscriptPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
