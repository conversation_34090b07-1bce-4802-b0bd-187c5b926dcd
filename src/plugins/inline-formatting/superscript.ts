import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Superscript formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'superscript',
  name: 'Superscript',
  description: 'Format selected text as superscript',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'superscript',
      command: 'superscript',
      icon: 'x²',
      label: 'Superscript',
      tooltip: 'Superscript (Ctrl/⌘+.)',
      group: 'formatting',
      ariaLabel: 'Superscript formatting',
    }
  ],
  shortcuts: [
    {
      command: 'superscript',
      key: '.',
      ctrlKey: true,
      description: 'Apply superscript formatting'
    }
  ]
};

/**
 * Superscript formatting plugin implementation
 * Uses <sup> element for semantic HTML
 */
export class SuperscriptPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the superscript command
   * @param command The command to handle
   */
  public handleCommand(command: string): void {
    if (command === 'superscript' && this.editor) {
      this.editor.format('superscript');
    }
  }
}

// Create and export the plugin instance directly
const plugin = new SuperscriptPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
