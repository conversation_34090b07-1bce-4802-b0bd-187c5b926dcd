import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Strikethrough formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'strikethrough',
  name: 'Strikethrough',
  description: 'Apply strikethrough formatting to selected text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'strikethrough',
      command: 'strikethrough',
      icon: 'S',
      label: 'Strikethrough',
      tooltip: 'Strikethrough (Ctrl/⌘+Shift+X)',
      group: 'formatting',
      ariaLabel: 'Strikethrough formatting',
    }
  ],
  shortcuts: [
    {
      command: 'strikethrough',
      key: 'x',
      ctrlKey: true,
      shiftKey: true,
      description: 'Apply strikethrough formatting'
    }
  ]
};

/**
 * Strikethrough formatting plugin implementation
 * Uses <s> element for semantic HTML
 */
export class StrikethroughPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the strikethrough command
   * @param command The command to handle
   */
  public handleCommand(command: string): void {
    if (command === 'strikethrough' && this.editor) {
      // In real implementation, you would wrap selection with <s> element
      this.editor.format('strikeThrough');
    }
  }
}

// Create and export the plugin instance directly
const plugin = new StrikethroughPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
