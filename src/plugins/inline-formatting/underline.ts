import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Underline formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'underline',
  name: 'Underline',
  description: 'Apply underline formatting to selected text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'underline',
      command: 'underline',
      icon: 'U̲',
      label: 'Underline',
      tooltip: 'Underline (Ctrl/⌘+U)',
      group: 'formatting',
      ariaLabel: 'Underline formatting',
    }
  ],
  shortcuts: [
    {
      command: 'underline',
      key: 'u',
      ctrlKey: true,
      description: 'Apply underline formatting'
    }
  ]
};

/**
 * Underline formatting plugin implementation
 */
export class UnderlinePlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the underline command
   * @param command The command to handle
   */
  public handleCommand(command: string): void {
    if (command === 'underline' && this.editor) {
      this.editor.format('underline');
    }
  }
}

// Create and export the plugin instance directly
const plugin = new UnderlinePlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
