import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Inline code formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'inline-code',
  name: 'Inline Code',
  description: 'Format selected text as inline code',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'inline-code',
      command: 'inlineCode',
      icon: '</>',
      label: 'Code',
      tooltip: 'Inline Code (Ctrl/⌘+`)',
      group: 'formatting',
      ariaLabel: 'Inline code formatting',
    }
  ],
  shortcuts: [
    {
      command: 'inlineCode',
      key: '`',
      ctrlKey: true,
      description: 'Apply inline code formatting'
    }
  ]
};

/**
 * Inline code formatting plugin implementation
 * Uses <code> element for semantic HTML
 */
export class InlineCodePlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the inline code command
   * @param command The command to handle
   */
  public handleCommand(command: string): void {
    if (command === 'inlineCode' && this.editor) {
      this.applyInlineCodeFormatting();
    }
  }
  
  /**
   * Apply inline code formatting to the selected text
   * Refactored to use the editor's format API.
   */
  private applyInlineCodeFormatting(): void {
    if (!this.editor) return;
    this.editor.format('inlineCode'); 
  }
}

// Create and export the plugin instance directly
const plugin = new InlineCodePlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
