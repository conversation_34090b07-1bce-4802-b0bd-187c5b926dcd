import { BasePlugin, PluginConfig } from '../base-plugin';
/**
 * Text color plugin configuration
 */
const config: PluginConfig = {
  id: 'text-color',
  name: 'Text Color',
  description: 'Change the text color or highlight color',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'text-color',
      command: 'textColor',
      icon: '🎨',
      label: 'Color',
      tooltip: 'Text/Highlight Color',
      group: 'formatting',
      ariaLabel: 'Change text or background color',
    }
  ]
};

/**
 * Text color and highlight color plugin implementation
 */
export class TextColorPlugin extends BasePlugin {
  private colorPalette: HTMLElement | null = null;
  private isColorPaletteVisible = false;
  private selectedTab: 'foreground' | 'background' = 'foreground';
  private readonly colors = [
    '#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef', '#f3f3f3', '#ffffff',
    '#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#4a86e8', '#0000ff', '#9900ff', '#ff00ff'
  ];
  private boundHandleDocumentClick: (event: MouseEvent) => void;

  constructor() {
    super(config);
    // Bind the handler once in the constructor
    this.boundHandleDocumentClick = this.handleDocumentClick.bind(this);
  }

  /**
   * Handle the text color command
   * @param command The command to handle
   */
  public handleCommand(command: string): void {
    if (command === 'textColor') {
      this.toggleColorPalette();
    }
  }

  /**
   * Create the color palette component DOM structure.
   * Styles are now applied via src/styles/plugins.css
   */
  private createColorPalette(): HTMLElement {
    const palette = document.createElement('div');
    palette.className = 'absolute z-[1000] bg-white dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded-md shadow-lg p-2 w-[220px] text-gray-800 dark:text-slate-100';
    palette.setAttribute('role', 'dialog');
    palette.setAttribute('aria-modal', 'true');
    palette.setAttribute('aria-label', 'Color palette');

    // Create tabs
    const tabs = document.createElement('div');
    tabs.className = 'flex mb-2 border-b border-gray-300 dark:border-slate-600';
    tabs.setAttribute('role', 'tablist');
    tabs.setAttribute('aria-label', 'Color type');

    const foregroundTab = document.createElement('button'); // Changed to button for better semantics
    foregroundTab.type = 'button';
    foregroundTab.className = 'px-2 py-1 cursor-pointer border-b-2 text-sm border-blue-500 dark:border-blue-400 font-semibold text-blue-600 dark:text-blue-400 -mb-px'; // Active by default
    foregroundTab.textContent = 'Text Color';
    foregroundTab.setAttribute('role', 'tab');
    foregroundTab.setAttribute('aria-selected', 'true');
    foregroundTab.setAttribute('aria-controls', 'color-grid-panel');
    foregroundTab.setAttribute('data-tab', 'foreground');
    foregroundTab.tabIndex = 0;
    foregroundTab.addEventListener('click', () => this.switchTab('foreground'));
    foregroundTab.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') this.switchTab('foreground');
    });

    const backgroundTab = document.createElement('button'); // Changed to button
    backgroundTab.type = 'button';
    backgroundTab.className = 'px-2 py-1 cursor-pointer border-b-2 border-transparent -mb-px text-sm text-gray-600 dark:text-slate-300 hover:border-gray-300 dark:hover:border-slate-500'; // Inactive by default
    backgroundTab.textContent = 'Highlight';
    backgroundTab.setAttribute('role', 'tab');
    backgroundTab.setAttribute('aria-selected', 'false');
    backgroundTab.setAttribute('aria-controls', 'color-grid-panel');
    backgroundTab.setAttribute('data-tab', 'background');
    backgroundTab.tabIndex = -1;
    backgroundTab.addEventListener('click', () => this.switchTab('background'));
    backgroundTab.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') this.switchTab('background');
    });

    tabs.appendChild(foregroundTab);
    tabs.appendChild(backgroundTab);
    palette.appendChild(tabs);

    // Create color grid
    const colorGrid = document.createElement('div');
    colorGrid.className = 'grid grid-cols-10 gap-1';
    colorGrid.id = 'color-grid-panel'; // Shared panel for colors
    colorGrid.setAttribute('role', 'grid');
    colorGrid.setAttribute('aria-label', 'Color swatches');

    this.colors.forEach((color) => {
      const colorButton = document.createElement('button');
      colorButton.type = 'button';
      colorButton.className = 'w-[18px] h-[18px] rounded-sm cursor-pointer border border-black/10 dark:border-white/10 box-border hover:scale-110 hover:border-gray-700 dark:hover:border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400';
      colorButton.style.backgroundColor = color;
      colorButton.setAttribute('data-color', color);
      colorButton.setAttribute('aria-label', `Color ${color}`);
      colorButton.setAttribute('role', 'gridcell');
      colorButton.addEventListener('click', () => this.applyColor(color));
      colorGrid.appendChild(colorButton);
    });

    palette.appendChild(colorGrid);

    // Trap focus within the dialog
    palette.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideColorPalette();
        // Return focus to the trigger button
        const triggerButton = document.querySelector('[data-command="textColor"]') as HTMLElement;
        triggerButton?.focus();
      }
      // Basic focus trapping - might need a more robust solution
      if (e.key === 'Tab') {
        const focusableElements = palette.querySelectorAll<HTMLElement>('[tabindex="0"], button');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    });

    return palette;
  }

  /**
   * Toggle the visibility of the color palette
   */
  private toggleColorPalette(): void {
    if (this.isColorPaletteVisible) {
      this.hideColorPalette();
    } else {
      this.showColorPalette();
    }
  }

  /**
   * Show the color palette
   */
  private showColorPalette(): void {
    if (!this.colorPalette) {
      this.colorPalette = this.createColorPalette();
      document.body.appendChild(this.colorPalette);

      // Position the palette near the button
      const button = document.querySelector('[data-command="textColor"]') as HTMLElement;

      if (button) {
        const rect = button.getBoundingClientRect();
        this.colorPalette.style.top = `${rect.bottom + window.scrollY + 5}px`;
        this.colorPalette.style.left = `${rect.left + window.scrollX}px`;
      }

      // Add document click listener to close the palette when clicking outside
      // Use the bound handler
      document.addEventListener('click', this.boundHandleDocumentClick, true); // Use capture phase
    }
    this.isColorPaletteVisible = true;
    // Set focus to the first focusable element in the palette
    (this.colorPalette?.querySelector('[tabindex="0"], button') as HTMLElement)?.focus();
  }

  /**
   * Hide the color palette
   */
  private hideColorPalette(): void {
    if (this.colorPalette && this.colorPalette.parentNode) {
      this.colorPalette.parentNode.removeChild(this.colorPalette);
      // Remove the listener using the same bound handler and phase
      document.removeEventListener('click', this.boundHandleDocumentClick, true);
      this.colorPalette = null;
    }
    this.isColorPaletteVisible = false;
  }

  /**
   * Handle document click to close the palette when clicking outside
   */
  private handleDocumentClick(event: MouseEvent): void {
    // Check if the click is outside the palette AND outside the trigger button
    const triggerButton = document.querySelector('[data-command="textColor"]') as HTMLElement;
    const clickedOutsidePalette = this.colorPalette && !this.colorPalette.contains(event.target as Node);
    const clickedOutsideTrigger = triggerButton && !triggerButton.contains(event.target as Node);

    if (clickedOutsidePalette && clickedOutsideTrigger) {
      this.hideColorPalette();
    }
  }

  /**
   * Switch between foreground and background color tabs
   */
  private switchTab(tab: 'foreground' | 'background'): void {
    this.selectedTab = tab;

    if (this.colorPalette) {
      const tabs = this.colorPalette.querySelectorAll<HTMLElement>('.tab');
      tabs.forEach((tabElement) => {
        const isSelected = tabElement.getAttribute('data-tab') === tab;
        tabElement.classList.toggle('active', isSelected);
        tabElement.setAttribute('aria-selected', isSelected.toString());
        // Update tabindex for focus management
        tabElement.tabIndex = isSelected ? 0 : -1;
      });
      // Optionally update aria-label of the grid if content changes significantly
      const grid = this.colorPalette.querySelector<HTMLElement>('.colors');
      if (grid) {
        grid.setAttribute('aria-label', tab === 'foreground' ? 'Text color swatches' : 'Highlight color swatches');
      }
    }
  }

  /**
   * Apply the selected color to the text or background
   */
  private applyColor(color: string): void {
    if (!this.editor) return;

    if (this.selectedTab === 'foreground') {
      this.editor.format('foreColor', color);
    } else {
      this.editor.format('hiliteColor', color);
    }

    this.hideColorPalette();
    // Return focus to the editor after applying color
    this.editor?.focus();
  }

  /**
   * Clean up resources when the plugin is destroyed
   */
  protected onDestroy(): void {
    // Ensure palette is removed if it exists
    this.hideColorPalette();
    // The click listener is removed in hideColorPalette
  }
}

// Create and export the plugin instance directly
const plugin = new TextColorPlugin();

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
